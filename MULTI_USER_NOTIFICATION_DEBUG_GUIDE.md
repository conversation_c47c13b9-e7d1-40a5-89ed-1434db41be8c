# 多用户通知问题调试指南

## 🔍 问题现象

用户反馈：**用两个浏览器登录不同的账号，只有一个账号能正常收到添加好友的通知！**

## 🧪 调试步骤

### 1. **准备测试环境**

1. **打开两个不同的浏览器窗口**（建议使用不同的浏览器，如Chrome和Firefox）
2. **分别登录不同的用户账号**：
   - 浏览器1：登录 `admin` 用户
   - 浏览器2：登录 `test` 用户

### 2. **检查Socket连接状态**

在两个浏览器中分别访问：`http://localhost:5173/multi-user-test`

检查以下信息：
- ✅ **用户名和用户ID** - 确认登录的是不同用户
- ✅ **Socket连接状态** - 两个用户都应该显示"已连接"
- ✅ **Socket ID** - 两个用户应该有不同的Socket ID

### 3. **测试Socket连接**

在两个浏览器的测试页面中：
1. 点击 **"测试Socket连接"** 按钮
2. 点击 **"重新设置事件监听器"** 按钮
3. 观察事件日志中的输出

### 4. **发送好友请求测试**

#### 方法1：使用测试页面
1. 在浏览器1（admin用户）中：
   - 输入目标用户名：`test`
   - 点击 **"发送好友请求"** 按钮
   
2. 观察浏览器2（test用户）是否收到通知：
   - 通知图标是否显示红点
   - 事件日志是否显示收到好友请求
   - 是否有桌面通知、Toast通知等

#### 方法2：使用好友管理页面
1. 在浏览器1中访问：`http://localhost:5173/friends`
2. 搜索并发送好友请求给另一个用户
3. 观察浏览器2是否收到通知

### 5. **检查后端日志**

观察后端控制台输出，查找以下关键信息：

#### Socket连接日志：
```
User admin connected with session [SESSION_ID]
User test connected with session [SESSION_ID]
```

#### 好友请求发送日志：
```
Sending friend request notification:
  - To room: user_[USER_ID]
  - Request data: {...}
✅ Friend request notification sent to user [USER_ID]
```

### 6. **常见问题排查**

#### 问题1：Socket连接不稳定
**症状**：用户频繁连接/断开
**解决方法**：
- 刷新页面重新建立连接
- 检查网络连接
- 重启后端服务器

#### 问题2：用户房间加入失败
**症状**：后端显示发送通知，但前端收不到
**可能原因**：
- 用户没有正确加入到 `user_{user_id}` 房间
- Socket ID与用户ID映射错误

#### 问题3：事件监听器丢失
**症状**：Socket连接正常，但事件不触发
**解决方法**：
- 在测试页面点击"重新设置事件监听器"
- 检查浏览器控制台是否有JavaScript错误

#### 问题4：用户ID映射错误
**症状**：通知发送到错误的用户
**检查方法**：
- 确认后端日志中的用户ID是否正确
- 检查前端显示的用户ID是否与后端一致

## 🔧 调试工具

### 1. **多用户测试页面**
- 访问：`http://localhost:5173/multi-user-test`
- 功能：检查Socket状态、发送测试请求、查看事件日志

### 2. **Socket调试页面**
- 访问：`http://localhost:5173/socket-debug`
- 功能：详细的Socket连接调试

### 3. **浏览器开发者工具**
- 打开控制台查看JavaScript错误
- 检查网络请求是否成功
- 查看WebSocket连接状态

## 📝 预期的正常流程

### 发送方（admin用户）：
1. 在好友管理页面搜索用户
2. 发送好友请求
3. 后端创建好友请求记录
4. 后端发送Socket事件到接收方房间

### 接收方（test用户）：
1. Socket接收到 `friend_request_received` 事件
2. 前端事件监听器处理事件
3. 更新通知状态（添加到notificationsStore）
4. 显示各种通知提示：
   - 通知图标红点
   - 桌面通知
   - Toast通知
   - 音效提示
   - 页面标题更新

## 🎯 可能的问题原因

### 1. **Socket房间问题**
- 用户没有正确加入到自己的房间
- 房间名称格式错误（应该是 `user_{user_id}`）

### 2. **事件监听器问题**
- 全局事件监听器被页面级监听器覆盖
- 事件监听器在Socket重连后丢失

### 3. **用户认证问题**
- JWT token过期或无效
- 用户ID获取错误

### 4. **前端状态管理问题**
- notificationsStore状态不同步
- 组件间通信问题

## 🚀 解决方案

根据调试结果，可能需要：

1. **增强Socket连接稳定性**
2. **改进事件监听器管理**
3. **添加更多调试日志**
4. **优化用户房间管理**

请按照上述步骤进行调试，并记录具体的问题现象，这样我们可以针对性地解决问题。
