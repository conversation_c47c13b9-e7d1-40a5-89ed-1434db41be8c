# 好友请求通知功能实现

## 功能概述

当用户收到好友请求时，系统会通过多种方式提供通知提示，确保用户能够及时发现并处理好友请求。

## 已实现的通知功能

### 1. 🔔 通知图标红点提示
- **位置**: 右上角铃铛图标
- **功能**: 显示未读通知数量的红色徽章
- **特点**: 
  - 实时更新未读数量
  - 支持99+的大数量显示
  - 包含好友请求和系统通知的总数

### 2. ✨ 新通知动画效果
- **动画类型**: 
  - 跳动动画 (`animate-bounce`)
  - 脉冲动画 (`animate-pulse`)
  - 扩散动画 (`animate-ping`)
- **触发条件**: 收到新的好友请求时
- **持续时间**: 3秒后自动停止
- **交互**: 点击通知图标时立即停止动画

### 3. 🖥️ 浏览器原生桌面通知
- **功能**: 显示系统级桌面通知
- **内容**: 
  - 标题: "{发送者用户名} 申请添加您为好友"
  - 内容: "点击查看详情"
  - 图标: 应用favicon
- **权限**: 自动请求通知权限
- **交互**: 点击通知聚焦到应用窗口

### 4. 📱 页面内Toast弹窗通知
- **位置**: 页面右上角
- **样式**: 
  - 白色卡片样式
  - 阴影和边框
  - 进度条显示剩余时间
- **功能**: 
  - 自动消失（5秒）
  - 手动关闭按钮
  - 快捷操作按钮（查看请求、稍后处理）
- **动画**: 从右侧滑入/滑出

### 5. 🔊 音效提示
- **技术**: Web Audio API
- **音效**: 自定义合成的通知音效
- **特点**: 
  - 双音调（800Hz → 600Hz）
  - 渐入渐出效果
  - 可启用/禁用
- **兼容性**: 自动处理音频上下文状态

### 6. 📄 页面标题未读数量提示
- **格式**: `(未读数量) 1v1 Chat`
- **更新**: 实时响应通知数量变化
- **重置**: 无未读通知时恢复原标题

## 技术实现

### 核心组件

1. **NotificationIcon.vue** - 通知图标组件
   - 红点徽章显示
   - 下拉通知列表
   - 动画效果控制

2. **NotificationToast.vue** - Toast通知组件
   - 弹窗样式和动画
   - 进度条和自动关闭
   - 交互按钮

### 服务层

1. **notificationService.ts** - 浏览器通知服务
   - 权限管理
   - 桌面通知显示
   - 兼容性处理

2. **audioService.ts** - 音频通知服务
   - Web Audio API封装
   - 音效合成和播放
   - 状态管理

3. **toastService.ts** - Toast管理服务
   - 动态组件创建
   - 生命周期管理
   - 多实例支持

### 状态管理

**notifications.ts** - Pinia状态管理
- 好友请求列表
- 系统通知列表
- 未读数量计算
- 状态更新方法

## 使用方式

### 自动触发
当收到好友请求的Socket事件时，系统会自动：
1. 更新通知状态
2. 显示所有类型的通知提示
3. 播放音效
4. 更新页面标题

### 手动测试
访问 `/notification-demo` 页面可以测试所有通知功能：
- 模拟好友请求
- 单独测试各种通知类型
- 查看当前状态
- 管理通知权限

## 配置选项

### 音效控制
```typescript
// 启用/禁用音效
audioService.setEnabled(true/false)

// 检查音效状态
audioService.isEnabled()
```

### 通知权限
```typescript
// 请求通知权限
await notificationService.requestPermission()

// 检查权限状态
notificationService.getPermission()
```

### Toast持续时间
```typescript
// 自定义显示时间（毫秒）
toastService.showFriendRequestToast(senderName, 8000)
```

## 浏览器兼容性

- **桌面通知**: 现代浏览器支持
- **音效**: Web Audio API支持的浏览器
- **动画**: CSS3支持的浏览器
- **Toast**: 所有现代浏览器

## 用户体验优化

1. **渐进增强**: 即使某些功能不支持，基础通知仍可用
2. **权限友好**: 自动请求权限，失败时不影响其他功能
3. **性能优化**: 音频上下文按需创建，Toast组件动态管理
4. **视觉反馈**: 多层次的视觉提示确保用户不会错过通知
5. **交互便利**: 点击通知可直接跳转到相关页面

## 演示页面

访问 `http://localhost:5173/notification-demo` 查看完整的通知功能演示。
