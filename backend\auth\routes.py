import secrets
import os
import uuid
from datetime import datetime
from flask import request, jsonify, make_response, current_app
from flask_jwt_extended import (
    create_access_token, jwt_required, get_jwt_identity,
    set_access_cookies, unset_jwt_cookies
)
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from werkzeug.utils import secure_filename
from PIL import Image

from . import auth_bp
from models import db, User, InviteCode, get_local_time

# 中文错误消息常量
ERROR_MESSAGES = {
    'NO_DATA': '未提供数据',
    'USERNAME_PASSWORD_REQUIRED': '用户名和密码为必填项',
    'USERNAME_LENGTH': '用户名必须为3-20个字符',
    'PASSWORD_LENGTH': '密码至少需要6个字符',
    'USERNAME_EXISTS': '用户名已存在',
    'INVALID_INVITE_CODE': '邀请码无效或已被使用',
    'REGISTRATION_FAILED': '注册失败',
    'INVALID_CREDENTIALS': '用户名或密码错误',
    'ACCOUNT_BANNED': '账户已被封禁',
    'LOGIN_FAILED': '登录失败',
    'USER_NOT_FOUND': '用户未找到',
    'INVALID_USER_ID': '无效的用户ID',
    'ANONYMOUS_USER_FAILED': '创建匿名用户失败',
    'UPLOAD_FAILED': '上传失败',
    'NO_FILE': '未选择文件',
    'INVALID_FILE_TYPE': '不支持的文件类型',
    'FILE_TOO_LARGE': '文件过大',
    'RATE_LIMIT_EXCEEDED': '请求过于频繁，请稍后再试',
    'INTERNAL_ERROR': '服务器内部错误'
}

# 速率限制装饰器
limiter = Limiter(key_func=get_remote_address)

@auth_bp.route('/register', methods=['POST'])
@limiter.limit("5 per minute")
def register():
    """用户注册"""
    data = request.get_json()

    if not data:
        return jsonify({'error': ERROR_MESSAGES['NO_DATA']}), 400

    username = data.get('username', '').strip()
    password = data.get('password', '')
    invite_code = data.get('invite_code', '').strip()

    # 验证输入
    if not username or not password:
        return jsonify({'error': ERROR_MESSAGES['USERNAME_PASSWORD_REQUIRED']}), 400

    if len(username) < 3 or len(username) > 20:
        return jsonify({'error': ERROR_MESSAGES['USERNAME_LENGTH']}), 400

    if len(password) < 6:
        return jsonify({'error': ERROR_MESSAGES['PASSWORD_LENGTH']}), 400

    # 检查用户名是否已存在
    if User.query.filter_by(username=username).first():
        return jsonify({'error': ERROR_MESSAGES['USERNAME_EXISTS']}), 400

    # 验证邀请码
    if invite_code:
        invite = InviteCode.query.filter_by(code=invite_code, is_used=False).first()
        if not invite:
            return jsonify({'error': ERROR_MESSAGES['INVALID_INVITE_CODE']}), 400
    
    try:
        # 创建用户
        user = User(
            username=username,
            is_anonymous=False
        )
        user.set_password(password)
        
        db.session.add(user)
        db.session.flush()  # 获取用户ID
        
        # 使用邀请码
        if invite_code and invite:
            invite.use_code(user.id)

            # 如果邀请码有创建者，建立对话关系
            if invite.creator_id and invite.creator_id != user.id:
                # 创建一条系统消息来记录邀请码使用
                from models import Message
                system_message_content = f"用户 {user.username} 通过邀请码 {invite.code}"
                if invite.note:
                    system_message_content += f"（{invite.note}）"
                system_message_content += " 加入了对话"

                # 发送给邀请码创建者的系统消息
                system_message = Message(
                    content=system_message_content,
                    sender_id=user.id,
                    recipient_id=invite.creator_id,
                    timestamp=get_local_time()
                )
                db.session.add(system_message)

        db.session.commit()

        # 如果创建了系统消息，通过Socket.IO实时推送给双方
        if invite_code and invite and invite.creator_id and invite.creator_id != user.id:
            try:
                from chat.socket_handlers import get_socketio
                socketio = get_socketio()

                if socketio is None:
                    print("Socket.IO instance not available")
                    return

                # 获取创建者信息
                creator = User.query.get(invite.creator_id)
                if creator:
                    # 获取系统消息的完整数据
                    system_message_data = system_message.to_dict()
                    system_message_data['sender_username'] = user.username
                    system_message_data['recipient_username'] = creator.username

                    # 只发送 new_conversation 事件，前端会处理消息和会话更新
                    # 发送新对话通知给邀请码创建者，确保会话列表立即更新
                    socketio.emit('new_conversation', {
                        'user': user.to_dict(),
                        'message': system_message_data
                    }, room=f"user_{invite.creator_id}")

                    # 同时发送给新注册的用户，让他们也能立即看到新对话
                    socketio.emit('new_conversation', {
                        'user': creator.to_dict(),
                        'message': system_message_data
                    }, room=f"user_{user.id}")

                    print(f"Registration system message and new conversation notification sent via Socket.IO to both creator {creator.username} and new user {user.username}")

            except Exception as e:
                print(f"Failed to send Socket.IO notification for registration: {e}")
        
        # 创建访问令牌
        access_token = create_access_token(identity=str(user.id))
        
        response = make_response(jsonify({
            'message': 'User registered successfully',
            'user': user.to_dict()
        }))
        
        # 设置JWT cookie
        set_access_cookies(response, access_token)
        
        return response, 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': ERROR_MESSAGES['REGISTRATION_FAILED']}), 500

@auth_bp.route('/login', methods=['POST'])
@limiter.limit("10 per minute")
def login():
    """用户登录"""
    data = request.get_json()

    if not data:
        return jsonify({'error': ERROR_MESSAGES['NO_DATA']}), 400

    username = data.get('username', '').strip()
    password = data.get('password', '')

    if not username or not password:
        return jsonify({'error': ERROR_MESSAGES['USERNAME_PASSWORD_REQUIRED']}), 400

    # 查找用户
    user = User.query.filter_by(username=username, is_anonymous=False).first()

    if not user or not user.check_password(password):
        return jsonify({'error': ERROR_MESSAGES['INVALID_CREDENTIALS']}), 401

    if user.banned:
        return jsonify({'error': ERROR_MESSAGES['ACCOUNT_BANNED']}), 403
    
    try:
        # 更新最后登录时间
        user.last_seen = get_local_time()
        db.session.commit()
        
        # 创建访问令牌
        access_token = create_access_token(identity=str(user.id))
        print(f"Login successful - user_id: {user.id}, token created")  # 调试信息

        response = make_response(jsonify({
            'message': 'Login successful',
            'user': user.to_dict()
        }))

        # 设置JWT cookie
        set_access_cookies(response, access_token)
        print(f"JWT cookie set for user: {user.username}")  # 调试信息

        return response
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': ERROR_MESSAGES['LOGIN_FAILED']}), 500

@auth_bp.route('/logout', methods=['POST'])
@jwt_required(optional=True)
def logout():
    """用户登出"""
    response = make_response(jsonify({'message': 'Logout successful'}))
    unset_jwt_cookies(response)

    # 清除匿名用户cookie
    response.set_cookie(
        current_app.config['ANONYMOUS_COOKIE_NAME'],
        '',
        expires=0,
        httponly=True,
        secure=current_app.config['JWT_COOKIE_SECURE']
    )

    return response

@auth_bp.route('/me', methods=['GET'])
@jwt_required()
def get_current_user():
    """获取当前用户信息"""
    user_id_str = get_jwt_identity()
    try:
        user_id = int(user_id_str)
        user = User.query.get(user_id)
    except (ValueError, TypeError):
        return jsonify({'error': ERROR_MESSAGES['INVALID_USER_ID']}), 400

    if not user:
        return jsonify({'error': ERROR_MESSAGES['USER_NOT_FOUND']}), 404
    
    # 更新最后活跃时间
    user.last_seen = get_local_time()
    db.session.commit()
    
    return jsonify({'user': user.to_dict()})

@auth_bp.route('/anonymous', methods=['POST'])
def create_anonymous_user():
    """创建或获取匿名用户"""
    # 检查是否已有匿名用户cookie
    cookie_id = request.cookies.get(current_app.config['ANONYMOUS_COOKIE_NAME'])
    
    if cookie_id:
        # 查找现有匿名用户
        user = User.query.filter_by(cookie_id=cookie_id, is_anonymous=True).first()
        if user and not user.banned:
            # 更新最后活跃时间
            user.last_seen = get_local_time()
            db.session.commit()
            
            # 创建访问令牌
            access_token = create_access_token(identity=str(user.id))
            
            response = make_response(jsonify({
                'message': 'Anonymous user found',
                'user': user.to_dict()
            }))
            
            set_access_cookies(response, access_token)
            return response
    
    try:
        # 生成新的cookie ID
        cookie_id = secrets.token_hex(16)
        
        # 创建新的匿名用户
        user = User.create_anonymous_user(cookie_id)
        db.session.add(user)
        db.session.commit()
        
        # 创建访问令牌
        access_token = create_access_token(identity=str(user.id))
        
        response = make_response(jsonify({
            'message': 'Anonymous user created',
            'user': user.to_dict()
        }))
        
        # 设置JWT cookie和匿名用户cookie
        set_access_cookies(response, access_token)
        response.set_cookie(
            current_app.config['ANONYMOUS_COOKIE_NAME'],
            cookie_id,
            max_age=current_app.config['ANONYMOUS_COOKIE_MAX_AGE'],
            httponly=True,
            secure=current_app.config['JWT_COOKIE_SECURE']
        )
        
        return response, 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': ERROR_MESSAGES['ANONYMOUS_USER_FAILED']}), 500

@auth_bp.route('/check-username', methods=['POST'])
def check_username():
    """检查用户名是否可用"""
    data = request.get_json()
    username = data.get('username', '').strip()

    if not username:
        return jsonify({'available': False, 'error': '用户名为必填项'})

    if len(username) < 3 or len(username) > 20:
        return jsonify({'available': False, 'error': ERROR_MESSAGES['USERNAME_LENGTH']})

    # 检查是否已存在
    exists = User.query.filter_by(username=username).first() is not None

    return jsonify({
        'available': not exists,
        'username': username
    })

@auth_bp.route('/validate-invite', methods=['POST'])
def validate_invite_code():
    """验证邀请码"""
    data = request.get_json()
    code = data.get('code', '').strip()

    if not code:
        return jsonify({'valid': False, 'error': '邀请码为必填项'})

    invite = InviteCode.query.filter_by(code=code, is_used=False).first()

    result = {
        'valid': invite is not None,
        'code': code
    }

    # 如果邀请码有效，返回更多信息
    if invite:
        result['invite'] = invite.to_dict()

    return jsonify(result)

@auth_bp.route('/use-invite', methods=['POST'])
@jwt_required()
def use_invite_code():
    """使用邀请码开始对话"""
    user_id = get_jwt_identity()
    data = request.get_json()
    code = data.get('code', '').strip()

    if not code:
        return jsonify({'error': '邀请码为必填项'}), 400

    invite = InviteCode.query.filter_by(code=code, is_used=False).first()

    if not invite:
        return jsonify({'error': '邀请码无效或已被使用'}), 404

    # 不能使用自己创建的邀请码
    if invite.creator_id == user_id:
        return jsonify({'error': '不能使用自己创建的邀请码'}), 400

    # 如果邀请码没有创建者，不能用于对话
    if not invite.creator_id:
        return jsonify({'error': '此邀请码不支持对话功能'}), 400

    try:
        # 标记邀请码为已使用
        invite.use_code(user_id)

        # 获取当前用户和创建者信息
        current_user = User.query.get(user_id)
        creator = User.query.get(invite.creator_id)

        if not creator:
            return jsonify({'error': '邀请码创建者不存在'}), 404

        # 创建系统消息记录邀请码使用
        from models import Message
        system_message_content = f"用户 {current_user.username} 通过邀请码 {invite.code}"
        if invite.note:
            system_message_content += f"（{invite.note}）"
        system_message_content += " 加入了对话"

        # 发送给邀请码创建者的系统消息
        system_message = Message(
            content=system_message_content,
            sender_id=user_id,
            recipient_id=invite.creator_id,
            timestamp=get_local_time()
        )
        db.session.add(system_message)

        db.session.commit()

        # 通过Socket.IO实时推送系统消息给双方
        try:
            from chat.socket_handlers import get_socketio
            socketio = get_socketio()

            if socketio is None:
                print("Socket.IO instance not available")
                return

            # 获取系统消息的完整数据
            system_message_data = system_message.to_dict()
            system_message_data['sender_username'] = current_user.username
            system_message_data['recipient_username'] = creator.username

            # 只发送 new_conversation 事件，前端会处理消息和会话更新
            # 发送新对话通知给邀请码创建者，确保会话列表立即更新
            socketio.emit('new_conversation', {
                'user': current_user.to_dict(),
                'message': system_message_data
            }, room=f"user_{invite.creator_id}")

            # 同时发送给使用邀请码的用户，让他们也能立即看到新对话
            socketio.emit('new_conversation', {
                'user': creator.to_dict(),
                'message': system_message_data
            }, room=f"user_{user_id}")

            print(f"System message and new conversation notification sent via Socket.IO to both creator {creator.username} and user {current_user.username}")

        except Exception as e:
            print(f"Failed to send Socket.IO notification: {e}")

        return jsonify({
            'message': '邀请码使用成功',
            'creator': creator.to_dict(),
            'invite': invite.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '使用邀请码失败'}), 500

@auth_bp.route('/avatar', methods=['POST'])
@jwt_required()
def upload_avatar():
    """上传头像"""
    try:
        current_user_id = int(get_jwt_identity())
        user = User.query.get(current_user_id)

        if not user:
            return jsonify({'error': ERROR_MESSAGES['USER_NOT_FOUND']}), 404

        if 'avatar' not in request.files:
            return jsonify({'error': ERROR_MESSAGES['NO_FILE']}), 400

        file = request.files['avatar']
        if file.filename == '':
            return jsonify({'error': ERROR_MESSAGES['NO_FILE']}), 400

        # 检查文件类型
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
        if not ('.' in file.filename and
                file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({'error': ERROR_MESSAGES['INVALID_FILE_TYPE']}), 400

        # 创建上传目录
        upload_dir = os.path.join(current_app.root_path, 'static', 'avatars')
        os.makedirs(upload_dir, exist_ok=True)

        # 生成唯一文件名
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        filename = f"{uuid.uuid4().hex}.{file_extension}"
        file_path = os.path.join(upload_dir, filename)

        # 保存并处理图片
        file.save(file_path)

        # 使用PIL处理图片，确保是120x120
        try:
            with Image.open(file_path) as img:
                # 转换为RGB模式（处理RGBA等格式）
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 调整大小为120x120
                img = img.resize((120, 120), Image.Resampling.LANCZOS)

                # 保存处理后的图片
                img.save(file_path, 'JPEG', quality=90)
        except Exception as e:
            # 如果图片处理失败，删除文件
            if os.path.exists(file_path):
                os.remove(file_path)
            return jsonify({'error': f'图片处理失败: {str(e)}'}), 400

        # 删除旧头像文件
        if user.avatar and user.avatar != 'default.png':
            old_avatar_path = os.path.join(upload_dir, user.avatar)
            if os.path.exists(old_avatar_path):
                try:
                    os.remove(old_avatar_path)
                except:
                    pass  # 忽略删除失败

        # 更新用户头像
        user.avatar = filename
        db.session.commit()

        return jsonify({
            'message': '头像上传成功',
            'avatar_url': f'/static/avatars/{filename}',
            'user': user.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': ERROR_MESSAGES['UPLOAD_FAILED']}), 500
