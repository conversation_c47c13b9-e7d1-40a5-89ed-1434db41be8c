from datetime import datetime
import os
import uuid
from flask import request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import or_, and_, case
from sqlalchemy.orm import joinedload
from PIL import Image

from . import chat_bp
from models import db, User, Message, ChatLink, MessageReadStatus, get_local_time

@chat_bp.route('/messages', methods=['GET'])
@jwt_required()
def get_messages():
    """获取聊天消息"""
    user_id = get_jwt_identity()
    other_user_id = request.args.get('user_id', type=int)
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    
    if not other_user_id:
        return jsonify({'error': 'user_id is required'}), 400
    
    # 验证对方用户是否存在
    other_user = User.query.get(other_user_id)
    if not other_user:
        return jsonify({'error': 'User not found'}), 404
    
    # 查询两人之间的消息
    messages = Message.query.filter(
        or_(
            and_(Message.sender_id == user_id, Message.recipient_id == other_user_id),
            and_(Message.sender_id == other_user_id, Message.recipient_id == user_id)
        )
    ).order_by(Message.timestamp.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'messages': [msg.to_dict() for msg in reversed(messages.items)],
        'pagination': {
            'page': page,
            'pages': messages.pages,
            'per_page': per_page,
            'total': messages.total,
            'has_next': messages.has_next,
            'has_prev': messages.has_prev
        }
    })

@chat_bp.route('/messages', methods=['POST'])
@jwt_required()
def send_message():
    """发送消息"""
    user_id = get_jwt_identity()
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    content = data.get('content', '').strip()
    recipient_id = data.get('recipient_id')
    reply_to = data.get('reply_to')
    
    if not content:
        return jsonify({'error': 'Message content is required'}), 400
    
    if not recipient_id:
        return jsonify({'error': 'Recipient ID is required'}), 400

    if recipient_id == user_id:
        return jsonify({'error': 'Cannot send message to yourself'}), 400

    if len(content) > 1000:
        return jsonify({'error': 'Message too long (max 1000 characters)'}), 400
    
    # 验证发送者
    sender = User.query.get(user_id)
    if not sender:
        return jsonify({'error': 'Sender not found'}), 404
    
    if sender.banned:
        return jsonify({'error': 'You are banned from sending messages'}), 403
    
    if sender.muted:
        return jsonify({'error': 'You are muted'}), 403
    
    # 验证接收者
    recipient = User.query.get(recipient_id)
    if not recipient:
        return jsonify({'error': 'Recipient not found'}), 404

    # 检查是否还是好友关系
    from models import FriendRequest
    friend_relationship = FriendRequest.query.filter(
        or_(
            and_(FriendRequest.sender_id == user_id, FriendRequest.receiver_id == recipient_id),
            and_(FriendRequest.sender_id == recipient_id, FriendRequest.receiver_id == user_id)
        ),
        FriendRequest.status == 'accepted'
    ).first()

    if not friend_relationship:
        return jsonify({'error': '无法给对方发送信息：您已被删除好友'}), 403
    
    # 验证回复消息（如果有）
    if reply_to:
        parent_message = Message.query.get(reply_to)
        if not parent_message:
            return jsonify({'error': 'Parent message not found'}), 404
        
        # 确保回复的消息是在同一个对话中
        if not ((parent_message.sender_id == user_id and parent_message.recipient_id == recipient_id) or
                (parent_message.sender_id == recipient_id and parent_message.recipient_id == user_id)):
            return jsonify({'error': 'Invalid parent message'}), 400
    
    try:
        # 创建消息
        message = Message(
            content=content,
            sender_id=user_id,
            recipient_id=recipient_id,
            reply_to=reply_to
        )
        
        db.session.add(message)
        db.session.commit()
        
        return jsonify({
            'message': 'Message sent successfully',
            'data': message.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to send message'}), 500

@chat_bp.route('/messages/<int:message_id>/recall', methods=['PATCH'])
@jwt_required()
def recall_message(message_id):
    """撤回消息"""
    user_id = get_jwt_identity()

    message = Message.query.get(message_id)
    if not message:
        return jsonify({'error': 'Message not found'}), 404

    # 获取当前用户信息
    current_user = User.query.get(user_id)
    if not current_user:
        return jsonify({'error': 'User not found'}), 404

    # 检查权限：管理员可以撤回任何消息，普通用户只能撤回自己的消息
    if not current_user.is_admin and message.sender_id != user_id:
        return jsonify({'error': 'You can only recall your own messages'}), 403

    # 检查是否已经撤回
    if message.is_recalled:
        return jsonify({'error': 'Message already recalled'}), 400

    # 检查时间限制：普通用户只能撤回1分钟内的消息，管理员无时间限制
    if not current_user.is_admin:
        from datetime import timedelta
        time_limit = timedelta(minutes=1)
        if get_local_time() - message.timestamp > time_limit:
            return jsonify({'error': 'Message can only be recalled within 1 minute'}), 400

    try:
        message.recall()
        db.session.commit()

        return jsonify({
            'message': 'Message recalled successfully',
            'data': message.to_dict(current_user)
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to recall message'}), 500

@chat_bp.route('/conversations', methods=['GET'])
@jwt_required()
def get_conversations():
    """获取会话列表"""
    user_id = get_jwt_identity()
    
    # 查询用户参与的所有会话
    # 获取最近的消息作为会话预览
    from sqlalchemy import func
    
    # 子查询：获取每个会话的最新消息
    # 使用CASE语句创建会话对标识符（SQLite兼容）
    # 过滤掉自己给自己发消息的情况
    latest_messages = db.session.query(
        func.max(Message.id).label('latest_id')
    ).filter(
        and_(
            or_(Message.sender_id == user_id, Message.recipient_id == user_id),
            Message.sender_id != Message.recipient_id  # 排除自己给自己发消息
        )
    ).group_by(
        # 创建会话对标识符：使用较小和较大的用户ID
        case(
            (Message.sender_id < Message.recipient_id, Message.sender_id),
            else_=Message.recipient_id
        ),
        case(
            (Message.sender_id < Message.recipient_id, Message.recipient_id),
            else_=Message.sender_id
        )
    ).subquery()
    
    # 获取最新消息的详细信息
    conversations = db.session.query(Message).filter(
        Message.id.in_(latest_messages)
    ).order_by(Message.timestamp.desc()).all()
    
    result = []
    for msg in conversations:
        # 确定对话的另一方（注意类型转换，user_id是字符串）
        if int(msg.sender_id) == int(user_id):
            other_user_id = msg.recipient_id
        else:
            other_user_id = msg.sender_id

        # 过滤掉自己给自己发消息的情况
        if int(other_user_id) == int(user_id):
            continue

        other_user = User.query.get(other_user_id)

        if other_user:
            # 计算未读消息数量
            # 查询该会话中当前用户未读的消息数量
            unread_count = db.session.query(Message).filter(
                and_(
                    Message.sender_id == other_user_id,  # 对方发送的消息
                    Message.recipient_id == user_id,     # 发给当前用户的消息
                    ~Message.id.in_(                     # 不在已读列表中的消息
                        db.session.query(MessageReadStatus.message_id).filter(
                            MessageReadStatus.user_id == user_id
                        )
                    )
                )
            ).count()

            result.append({
                'user': other_user.to_dict(),
                'last_message': msg.to_dict(),
                'unread_count': unread_count
            })
    
    return jsonify({'conversations': result})

@chat_bp.route('/links', methods=['POST'])
@jwt_required()
def create_chat_link():
    """创建聊天链接"""
    user_id = get_jwt_identity()
    data = request.get_json() or {}

    expire_hours = data.get('expire_hours', 24)
    single_use = data.get('single_use', True)
    note = data.get('note', '').strip()

    # 验证参数
    if expire_hours < 1 or expire_hours > 168:  # 最多7天
        return jsonify({'error': '过期时间必须在1到168小时之间'}), 400

    # 备注长度限制
    if note and len(note) > 255:
        return jsonify({'error': '备注长度不能超过255个字符'}), 400

    try:
        link = ChatLink.create_link(
            creator_id=user_id,
            expire_hours=expire_hours,
            single_use=single_use,
            note=note if note else None
        )

        db.session.add(link)
        db.session.commit()

        return jsonify({
            'message': '聊天链接创建成功',
            'link': link.to_dict(),
            'url': f"/chat/{link.code}"
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '创建聊天链接失败'}), 500

@chat_bp.route('/links/<code>', methods=['GET'])
def validate_chat_link(code):
    """验证聊天链接"""
    print(f"Validating chat link with code: {code}")

    # 使用join来确保创建者信息被加载
    link = ChatLink.query.options(joinedload(ChatLink.creator)).filter_by(code=code).first()

    if not link:
        print(f"Chat link not found: {code}")
        return jsonify({'error': '聊天链接不存在'}), 404

    if not link.is_valid():
        print(f"Chat link invalid: {code}")
        return jsonify({'error': '聊天链接已过期或已被使用'}), 410

    try:
        link_dict = link.to_dict()
        print(f"Chat link validation successful: {link_dict}")

        return jsonify({
            'valid': True,
            'link': link_dict
        })
    except Exception as e:
        print(f"Error converting link to dict: {e}")
        return jsonify({'error': '链接数据处理失败'}), 500

@chat_bp.route('/links/<code>/use', methods=['POST'])
@jwt_required()
def use_chat_link(code):
    """使用聊天链接"""
    user_id = get_jwt_identity()
    
    link = ChatLink.query.filter_by(code=code).first()
    
    if not link:
        return jsonify({'error': '聊天链接不存在'}), 404

    if not link.is_valid():
        return jsonify({'error': '聊天链接已过期或已被使用'}), 410

    # 不能使用自己创建的链接
    if link.creator_id == user_id:
        return jsonify({'error': '不能使用自己创建的聊天链接'}), 400
    
    try:
        # 标记链接为已使用
        link.use_link()

        # 获取当前用户和创建者信息
        current_user = User.query.get(user_id)
        creator = User.query.get(link.creator_id)

        if not creator:
            return jsonify({'error': '链接创建者不存在'}), 404

        # 创建系统消息记录链接使用
        system_message_content = f"用户 {current_user.username} 通过聊天链接"
        if link.note:
            system_message_content += f"（{link.note}）"
        system_message_content += " 加入了对话"

        # 发送给链接创建者的系统消息
        system_message = Message(
            content=system_message_content,
            sender_id=user_id,
            recipient_id=link.creator_id,
            timestamp=get_local_time()
        )
        db.session.add(system_message)

        db.session.commit()

        # 通过Socket.IO实时推送系统消息给双方
        try:
            from chat.socket_handlers import get_socketio
            socketio = get_socketio()

            if socketio is None:
                print("Socket.IO instance not available")
                return

            # 获取系统消息的完整数据
            system_message_data = system_message.to_dict()
            system_message_data['sender_username'] = current_user.username
            system_message_data['recipient_username'] = creator.username

            # 只发送 new_conversation 事件，前端会处理消息和会话更新
            # 发送新对话通知给链接创建者，确保会话列表立即更新
            socketio.emit('new_conversation', {
                'user': current_user.to_dict(),
                'message': system_message_data
            }, room=f"user_{link.creator_id}")

            # 同时发送给使用链接的用户，让他们也能立即看到新对话
            socketio.emit('new_conversation', {
                'user': creator.to_dict(),
                'message': system_message_data
            }, room=f"user_{user_id}")

            print(f"Chat link system message and new conversation notification sent via Socket.IO to both creator {creator.username} and user {current_user.username}")

        except Exception as e:
            print(f"Failed to send Socket.IO notification for chat link: {e}")

        return jsonify({
            'message': '聊天链接使用成功',
            'creator': creator.to_dict(),
            'link': link.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '使用聊天链接失败'}), 500

@chat_bp.route('/users/search', methods=['GET'])
@jwt_required()
def search_users():
    """搜索用户"""
    query = request.args.get('q', '').strip()

    if not query or len(query) < 2:
        return jsonify({'error': '搜索关键词至少需要2个字符'}), 400

    # 搜索用户名包含查询字符串的用户
    users = User.query.filter(
        User.username.contains(query),
        User.banned == False
    ).limit(10).all()

    return jsonify({
        'users': [user.to_dict() for user in users]
    })

@chat_bp.route('/users/<int:user_id>', methods=['GET'])
@jwt_required()
def get_user_by_id(user_id):
    """通过ID获取用户信息"""
    current_user_id = int(get_jwt_identity())

    # 不能查询自己
    if user_id == current_user_id:
        return jsonify({'error': 'Cannot query yourself'}), 400

    user = User.query.get(user_id)
    if not user or user.banned:
        return jsonify({'error': 'User not found'}), 404

    return jsonify({
        'user': user.to_dict()
    })

@chat_bp.route('/messages/mark-read', methods=['POST'])
@jwt_required()
def mark_messages_as_read():
    """标记消息为已读"""
    user_id = get_jwt_identity()
    data = request.get_json()

    if not data or 'other_user_id' not in data:
        return jsonify({'error': 'other_user_id is required'}), 400

    other_user_id = data['other_user_id']

    try:
        # 获取该会话中对方发送给当前用户的所有未读消息
        unread_messages = db.session.query(Message).filter(
            and_(
                Message.sender_id == other_user_id,
                Message.recipient_id == user_id,
                ~Message.id.in_(
                    db.session.query(MessageReadStatus.message_id).filter(
                        MessageReadStatus.user_id == user_id
                    )
                )
            )
        ).all()

        # 为每条未读消息创建已读状态记录
        for message in unread_messages:
            # 检查是否已存在已读记录（防止重复）
            existing_read_status = MessageReadStatus.query.filter_by(
                message_id=message.id,
                user_id=user_id
            ).first()

            if not existing_read_status:
                read_status = MessageReadStatus(
                    message_id=message.id,
                    user_id=user_id
                )
                db.session.add(read_status)

        db.session.commit()

        return jsonify({
            'message': 'Messages marked as read',
            'marked_count': len(unread_messages)
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to mark messages as read'}), 500

@chat_bp.route('/conversations/<int:other_user_id>', methods=['DELETE'])
@jwt_required()
def delete_conversation(other_user_id):
    """删除与指定用户的对话"""
    current_user_id = int(get_jwt_identity())

    # 不能删除与自己的对话
    if other_user_id == current_user_id:
        return jsonify({'error': 'Cannot delete conversation with yourself'}), 400

    # 验证对方用户是否存在
    other_user = User.query.get(other_user_id)
    if not other_user:
        return jsonify({'error': 'User not found'}), 404

    try:
        # 查找两个用户之间的所有消息
        messages_to_delete = Message.query.filter(
            or_(
                and_(Message.sender_id == current_user_id, Message.recipient_id == other_user_id),
                and_(Message.sender_id == other_user_id, Message.recipient_id == current_user_id)
            )
        ).all()

        # 获取要删除的消息ID列表
        message_ids = [msg.id for msg in messages_to_delete]

        if message_ids:
            # 删除相关的已读状态记录
            MessageReadStatus.query.filter(
                MessageReadStatus.message_id.in_(message_ids)
            ).delete(synchronize_session=False)

            # 删除消息
            Message.query.filter(
                or_(
                    and_(Message.sender_id == current_user_id, Message.recipient_id == other_user_id),
                    and_(Message.sender_id == other_user_id, Message.recipient_id == current_user_id)
                )
            ).delete(synchronize_session=False)

        db.session.commit()

        return jsonify({
            'message': f'Conversation with user {other_user.username} deleted successfully',
            'deleted_messages_count': len(message_ids)
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to delete conversation'}), 500


@chat_bp.route('/upload-image', methods=['POST'])
@jwt_required()
def upload_image():
    """上传聊天图片"""
    try:
        current_user_id = int(get_jwt_identity())
        user = User.query.get(current_user_id)

        if not user:
            return jsonify({'error': '用户不存在'}), 404

        if 'image' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400

        # 检查文件类型
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
        if not ('.' in file.filename and
                file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({'error': '不支持的文件类型，请选择图片文件'}), 400

        # 检查文件大小 (10MB = 10 * 1024 * 1024 bytes)
        file.seek(0, 2)  # 移动到文件末尾
        file_size = file.tell()
        file.seek(0)  # 重置到文件开头

        if file_size > 10 * 1024 * 1024:
            return jsonify({'error': '文件大小不能超过10MB'}), 400

        # 创建上传目录
        upload_dir = os.path.join(current_app.root_path, 'static', 'chat_images')
        thumbnail_dir = os.path.join(current_app.root_path, 'static', 'chat_thumbnails')
        os.makedirs(upload_dir, exist_ok=True)
        os.makedirs(thumbnail_dir, exist_ok=True)

        # 生成唯一文件名
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        filename = f"{uuid.uuid4().hex}.{file_extension}"
        thumbnail_filename = f"thumb_{filename}"

        file_path = os.path.join(upload_dir, filename)
        thumbnail_path = os.path.join(thumbnail_dir, thumbnail_filename)

        # 保存原图
        file.save(file_path)

        # 生成缩略图和压缩原图
        try:
            with Image.open(file_path) as img:
                # 转换为RGB模式（处理RGBA等格式）
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 获取原始尺寸
                original_width, original_height = img.size

                # 压缩原图（如果太大的话）
                max_size = 1920  # 最大边长
                if max(original_width, original_height) > max_size:
                    ratio = max_size / max(original_width, original_height)
                    new_width = int(original_width * ratio)
                    new_height = int(original_height * ratio)
                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    img.save(file_path, 'JPEG', quality=85, optimize=True)

                # 生成缩略图 (200x200)
                thumbnail_size = (200, 200)
                img.thumbnail(thumbnail_size, Image.Resampling.LANCZOS)
                img.save(thumbnail_path, 'JPEG', quality=80, optimize=True)

        except Exception as e:
            # 如果图片处理失败，删除文件
            if os.path.exists(file_path):
                os.remove(file_path)
            if os.path.exists(thumbnail_path):
                os.remove(thumbnail_path)
            return jsonify({'error': f'图片处理失败: {str(e)}'}), 400

        # 返回图片URL
        image_url = f'/static/chat_images/{filename}'
        thumbnail_url = f'/static/chat_thumbnails/{thumbnail_filename}'

        return jsonify({
            'message': '图片上传成功',
            'image_url': image_url,
            'thumbnail_url': thumbnail_url,
            'filename': filename
        })

    except Exception as e:
        return jsonify({'error': f'上传失败: {str(e)}'}), 500
