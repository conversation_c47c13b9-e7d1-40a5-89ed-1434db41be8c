from datetime import datetime
from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import or_, and_

from . import groups_bp
from models import db, User, Group, GroupMembership, GroupMessage, get_local_time

@groups_bp.route('/', methods=['POST'])
@jwt_required()
def create_group():
    """创建群组"""
    current_user_id = int(get_jwt_identity())
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    # 检查用户是否有创建群组的权限
    current_user = User.query.get(current_user_id)
    if not current_user:
        return jsonify({'error': 'User not found'}), 404
    
    if not current_user.can_create_groups:
        return jsonify({'error': 'You do not have permission to create groups'}), 403
    
    name = data.get('name', '').strip()
    description = data.get('description', '').strip()
    is_private = data.get('is_private', False)
    max_members = data.get('max_members', 50)
    
    # 验证输入
    if not name:
        return jsonify({'error': 'Group name is required'}), 400
    
    if len(name) < 2 or len(name) > 100:
        return jsonify({'error': 'Group name must be 2-100 characters long'}), 400
    
    if len(description) > 500:
        return jsonify({'error': 'Description must be less than 500 characters'}), 400
    
    if max_members < 2 or max_members > 200:
        return jsonify({'error': 'Max members must be between 2 and 200'}), 400
    
    try:
        # 创建群组
        group = Group(
            name=name,
            description=description if description else None,
            creator_id=current_user_id,
            is_private=is_private,
            max_members=max_members
        )
        
        db.session.add(group)
        db.session.flush()  # 获取群组ID
        
        # 创建者自动成为群主
        membership = GroupMembership(
            group_id=group.id,
            user_id=current_user_id,
            role='owner'
        )
        
        db.session.add(membership)
        db.session.commit()
        
        return jsonify({
            'message': 'Group created successfully',
            'group': group.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to create group'}), 500

@groups_bp.route('/', methods=['GET'])
@jwt_required()
def get_user_groups():
    """获取用户的群组列表"""
    current_user_id = int(get_jwt_identity())
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    # 获取用户参与的群组
    groups = db.session.query(Group).join(GroupMembership).filter(
        GroupMembership.user_id == current_user_id
    ).order_by(Group.updated_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'groups': [group.to_dict() for group in groups.items],
        'pagination': {
            'page': page,
            'pages': groups.pages,
            'per_page': per_page,
            'total': groups.total,
            'has_next': groups.has_next,
            'has_prev': groups.has_prev
        }
    })

@groups_bp.route('/<int:group_id>', methods=['GET'])
@jwt_required()
def get_group_details(group_id):
    """获取群组详情"""
    current_user_id = int(get_jwt_identity())
    
    group = Group.query.get(group_id)
    if not group:
        return jsonify({'error': 'Group not found'}), 404
    
    # 检查用户是否为群组成员
    if not group.is_member(current_user_id):
        return jsonify({'error': 'You are not a member of this group'}), 403
    
    # 获取群组成员信息
    members = []
    for membership in group.memberships:
        member_dict = membership.to_dict()
        members.append(member_dict)
    
    group_dict = group.to_dict()
    group_dict['members'] = members
    
    return jsonify({'group': group_dict})

@groups_bp.route('/<int:group_id>/join', methods=['POST'])
@jwt_required()
def join_group(group_id):
    """加入群组"""
    current_user_id = int(get_jwt_identity())
    
    group = Group.query.get(group_id)
    if not group:
        return jsonify({'error': 'Group not found'}), 404
    
    # 检查群组是否为私有
    if group.is_private:
        return jsonify({'error': 'Cannot join private group without invitation'}), 403
    
    # 检查是否已经是成员
    if group.is_member(current_user_id):
        return jsonify({'error': 'Already a member of this group'}), 400
    
    # 检查群组是否已满
    if not group.can_join():
        return jsonify({'error': 'Group is full'}), 400
    
    try:
        membership = GroupMembership(
            group_id=group_id,
            user_id=current_user_id,
            role='member'
        )
        
        db.session.add(membership)
        db.session.commit()
        
        return jsonify({
            'message': 'Joined group successfully',
            'membership': membership.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to join group'}), 500

@groups_bp.route('/<int:group_id>/leave', methods=['POST'])
@jwt_required()
def leave_group(group_id):
    """离开群组"""
    current_user_id = int(get_jwt_identity())
    
    group = Group.query.get(group_id)
    if not group:
        return jsonify({'error': 'Group not found'}), 404
    
    membership = GroupMembership.query.filter_by(
        group_id=group_id,
        user_id=current_user_id
    ).first()
    
    if not membership:
        return jsonify({'error': 'You are not a member of this group'}), 400
    
    # 群主不能直接离开群组，需要先转让群主权限
    if membership.role == 'owner':
        return jsonify({'error': 'Group owner cannot leave. Transfer ownership first.'}), 400
    
    try:
        db.session.delete(membership)
        db.session.commit()
        
        return jsonify({'message': 'Left group successfully'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to leave group'}), 500

@groups_bp.route('/<int:group_id>/members/<int:user_id>', methods=['POST'])
@jwt_required()
def add_member(group_id, user_id):
    """添加成员到群组"""
    current_user_id = int(get_jwt_identity())
    
    group = Group.query.get(group_id)
    if not group:
        return jsonify({'error': 'Group not found'}), 404
    
    # 检查当前用户是否为群组管理员
    if not group.is_admin(current_user_id):
        return jsonify({'error': 'Only group admins can add members'}), 403
    
    # 检查要添加的用户是否存在
    user = User.query.get(user_id)
    if not user or user.banned:
        return jsonify({'error': 'User not found or banned'}), 404
    
    # 检查是否已经是成员
    if group.is_member(user_id):
        return jsonify({'error': 'User is already a member'}), 400
    
    # 检查群组是否已满
    if not group.can_join():
        return jsonify({'error': 'Group is full'}), 400
    
    try:
        membership = GroupMembership(
            group_id=group_id,
            user_id=user_id,
            role='member'
        )
        
        db.session.add(membership)
        db.session.commit()
        
        return jsonify({
            'message': 'Member added successfully',
            'membership': membership.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to add member'}), 500

@groups_bp.route('/<int:group_id>/members/<int:user_id>', methods=['DELETE'])
@jwt_required()
def remove_member(group_id, user_id):
    """从群组移除成员"""
    current_user_id = int(get_jwt_identity())
    
    group = Group.query.get(group_id)
    if not group:
        return jsonify({'error': 'Group not found'}), 404
    
    # 检查当前用户是否为群组管理员
    if not group.is_admin(current_user_id):
        return jsonify({'error': 'Only group admins can remove members'}), 403
    
    membership = GroupMembership.query.filter_by(
        group_id=group_id,
        user_id=user_id
    ).first()
    
    if not membership:
        return jsonify({'error': 'User is not a member of this group'}), 404
    
    # 不能移除群主
    if membership.role == 'owner':
        return jsonify({'error': 'Cannot remove group owner'}), 400
    
    # 管理员不能移除其他管理员（只有群主可以）
    current_membership = GroupMembership.query.filter_by(
        group_id=group_id,
        user_id=current_user_id
    ).first()
    
    if membership.role == 'admin' and current_membership.role != 'owner':
        return jsonify({'error': 'Only group owner can remove admins'}), 403
    
    try:
        db.session.delete(membership)
        db.session.commit()
        
        return jsonify({'message': 'Member removed successfully'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to remove member'}), 500

@groups_bp.route('/<int:group_id>/members/<int:user_id>/role', methods=['PUT'])
@jwt_required()
def update_member_role(group_id, user_id):
    """更新成员角色"""
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        return jsonify({'error': 'No data provided'}), 400

    new_role = data.get('role')
    if new_role not in ['member', 'admin']:
        return jsonify({'error': 'Invalid role. Must be member or admin'}), 400

    group = Group.query.get(group_id)
    if not group:
        return jsonify({'error': 'Group not found'}), 404

    # 只有群主可以更改成员角色
    current_membership = GroupMembership.query.filter_by(
        group_id=group_id,
        user_id=current_user_id
    ).first()

    if not current_membership or current_membership.role != 'owner':
        return jsonify({'error': 'Only group owner can change member roles'}), 403

    target_membership = GroupMembership.query.filter_by(
        group_id=group_id,
        user_id=user_id
    ).first()

    if not target_membership:
        return jsonify({'error': 'User is not a member of this group'}), 404

    if target_membership.role == 'owner':
        return jsonify({'error': 'Cannot change owner role'}), 400

    try:
        target_membership.role = new_role
        db.session.commit()

        return jsonify({
            'message': 'Member role updated successfully',
            'membership': target_membership.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to update member role'}), 500

@groups_bp.route('/<int:group_id>/messages', methods=['GET'])
@jwt_required()
def get_group_messages(group_id):
    """获取群组消息"""
    current_user_id = int(get_jwt_identity())
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)

    group = Group.query.get(group_id)
    if not group:
        return jsonify({'error': 'Group not found'}), 404

    # 检查用户是否为群组成员
    if not group.is_member(current_user_id):
        return jsonify({'error': 'You are not a member of this group'}), 403

    messages = GroupMessage.query.filter_by(
        group_id=group_id
    ).order_by(GroupMessage.timestamp.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # 反转消息顺序，使最新的消息在最后
    message_list = [msg.to_dict() for msg in reversed(messages.items)]

    return jsonify({
        'messages': message_list,
        'pagination': {
            'page': page,
            'pages': messages.pages,
            'per_page': per_page,
            'total': messages.total,
            'has_next': messages.has_next,
            'has_prev': messages.has_prev
        }
    })

@groups_bp.route('/<int:group_id>/messages', methods=['POST'])
@jwt_required()
def send_group_message(group_id):
    """发送群组消息"""
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        return jsonify({'error': 'No data provided'}), 400

    content = data.get('content', '').strip()
    reply_to = data.get('reply_to')

    if not content:
        return jsonify({'error': 'Message content is required'}), 400

    if len(content) > 2000:
        return jsonify({'error': 'Message too long'}), 400

    group = Group.query.get(group_id)
    if not group:
        return jsonify({'error': 'Group not found'}), 404

    # 检查用户是否为群组成员
    if not group.is_member(current_user_id):
        return jsonify({'error': 'You are not a member of this group'}), 403

    # 检查用户是否被禁言
    current_user = User.query.get(current_user_id)
    if current_user.muted:
        return jsonify({'error': 'You are muted and cannot send messages'}), 403

    # 如果是回复消息，验证被回复的消息是否存在且属于该群组
    if reply_to:
        parent_message = GroupMessage.query.filter_by(
            id=reply_to,
            group_id=group_id
        ).first()
        if not parent_message:
            return jsonify({'error': 'Parent message not found'}), 404

    try:
        message = GroupMessage(
            content=content,
            sender_id=current_user_id,
            group_id=group_id,
            reply_to=reply_to
        )

        db.session.add(message)
        db.session.commit()

        return jsonify({
            'message': 'Message sent successfully',
            'group_message': message.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to send message'}), 500

@groups_bp.route('/<int:group_id>', methods=['PUT'])
@jwt_required()
def update_group(group_id):
    """更新群组信息"""
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        return jsonify({'error': 'No data provided'}), 400

    group = Group.query.get(group_id)
    if not group:
        return jsonify({'error': 'Group not found'}), 404

    # 检查用户是否为群组管理员
    if not group.is_admin(current_user_id):
        return jsonify({'error': 'Only group admins can update group info'}), 403

    name = data.get('name')
    description = data.get('description')
    max_members = data.get('max_members')

    try:
        if name is not None:
            name = name.strip()
            if not name:
                return jsonify({'error': 'Group name cannot be empty'}), 400
            if len(name) < 2 or len(name) > 100:
                return jsonify({'error': 'Group name must be 2-100 characters long'}), 400
            group.name = name

        if description is not None:
            description = description.strip()
            if len(description) > 500:
                return jsonify({'error': 'Description must be less than 500 characters'}), 400
            group.description = description if description else None

        if max_members is not None:
            if max_members < group.get_member_count():
                return jsonify({'error': 'Cannot set max members below current member count'}), 400
            if max_members < 2 or max_members > 200:
                return jsonify({'error': 'Max members must be between 2 and 200'}), 400
            group.max_members = max_members

        group.updated_at = get_local_time()
        db.session.commit()

        return jsonify({
            'message': 'Group updated successfully',
            'group': group.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to update group'}), 500

@groups_bp.route('/<int:group_id>', methods=['DELETE'])
@jwt_required()
def delete_group(group_id):
    """删除群组"""
    current_user_id = int(get_jwt_identity())

    group = Group.query.get(group_id)
    if not group:
        return jsonify({'error': 'Group not found'}), 404

    # 只有群主可以删除群组
    membership = GroupMembership.query.filter_by(
        group_id=group_id,
        user_id=current_user_id
    ).first()

    if not membership or membership.role != 'owner':
        return jsonify({'error': 'Only group owner can delete the group'}), 403

    try:
        # 删除群组（级联删除会自动删除相关的成员关系和消息）
        db.session.delete(group)
        db.session.commit()

        return jsonify({'message': 'Group deleted successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to delete group'}), 500
