#!/usr/bin/env python3
"""
数据库迁移脚本：添加图片支持字段到 messages 和 group_messages 表
"""

import sqlite3
import os
import sys

def migrate_database():
    """执行数据库迁移"""
    # 获取数据库文件路径
    db_path = os.path.join(os.path.dirname(__file__), '..', 'instance', 'chat_dev.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查 messages 表是否已有图片相关字段
        cursor.execute("PRAGMA table_info(messages)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # 添加 message_type 字段
        if 'message_type' not in columns:
            print("添加 message_type 字段到 messages 表...")
            cursor.execute("ALTER TABLE messages ADD COLUMN message_type VARCHAR(20) DEFAULT 'text' NOT NULL")
            print("✓ messages 表 message_type 字段添加完成")
        else:
            print("messages 表已有 message_type 字段，跳过")
        
        # 添加 image_url 字段
        if 'image_url' not in columns:
            print("添加 image_url 字段到 messages 表...")
            cursor.execute("ALTER TABLE messages ADD COLUMN image_url VARCHAR(500)")
            print("✓ messages 表 image_url 字段添加完成")
        else:
            print("messages 表已有 image_url 字段，跳过")
        
        # 添加 image_thumbnail_url 字段
        if 'image_thumbnail_url' not in columns:
            print("添加 image_thumbnail_url 字段到 messages 表...")
            cursor.execute("ALTER TABLE messages ADD COLUMN image_thumbnail_url VARCHAR(500)")
            print("✓ messages 表 image_thumbnail_url 字段添加完成")
        else:
            print("messages 表已有 image_thumbnail_url 字段，跳过")
        
        # 检查 group_messages 表是否已有图片相关字段
        cursor.execute("PRAGMA table_info(group_messages)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # 添加 message_type 字段
        if 'message_type' not in columns:
            print("添加 message_type 字段到 group_messages 表...")
            cursor.execute("ALTER TABLE group_messages ADD COLUMN message_type VARCHAR(20) DEFAULT 'text' NOT NULL")
            print("✓ group_messages 表 message_type 字段添加完成")
        else:
            print("group_messages 表已有 message_type 字段，跳过")
        
        # 添加 image_url 字段
        if 'image_url' not in columns:
            print("添加 image_url 字段到 group_messages 表...")
            cursor.execute("ALTER TABLE group_messages ADD COLUMN image_url VARCHAR(500)")
            print("✓ group_messages 表 image_url 字段添加完成")
        else:
            print("group_messages 表已有 image_url 字段，跳过")
        
        # 添加 image_thumbnail_url 字段
        if 'image_thumbnail_url' not in columns:
            print("添加 image_thumbnail_url 字段到 group_messages 表...")
            cursor.execute("ALTER TABLE group_messages ADD COLUMN image_thumbnail_url VARCHAR(500)")
            print("✓ group_messages 表 image_thumbnail_url 字段添加完成")
        else:
            print("group_messages 表已有 image_thumbnail_url 字段，跳过")
        
        conn.commit()
        conn.close()
        
        print("数据库迁移完成！")
        return True
        
    except Exception as e:
        print(f"数据库迁移失败: {e}")
        return False

if __name__ == "__main__":
    success = migrate_database()
    sys.exit(0 if success else 1)
