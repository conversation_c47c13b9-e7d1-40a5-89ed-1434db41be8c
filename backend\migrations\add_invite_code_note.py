#!/usr/bin/env python3
"""
数据库迁移脚本：为邀请码添加备注和创建者字段
"""

import sqlite3
import os
import sys

def migrate_database():
    """执行数据库迁移"""
    # 获取数据库文件路径
    db_path = os.path.join(os.path.dirname(__file__), '..', 'instance', 'chat_dev.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查 invite_codes 表的字段
        cursor.execute("PRAGMA table_info(invite_codes)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # 添加 note 字段
        if 'note' not in columns:
            print("添加 note 字段到 invite_codes 表...")
            cursor.execute("ALTER TABLE invite_codes ADD COLUMN note VARCHAR(255)")
            print("✓ note 字段添加完成")
        else:
            print("note 字段已存在，跳过")
        
        # 添加 creator_id 字段
        if 'creator_id' not in columns:
            print("添加 creator_id 字段到 invite_codes 表...")
            cursor.execute("ALTER TABLE invite_codes ADD COLUMN creator_id INTEGER")
            print("✓ creator_id 字段添加完成")
        else:
            print("creator_id 字段已存在，跳过")
        
        conn.commit()
        conn.close()
        
        print("数据库迁移完成！")
        return True
        
    except Exception as e:
        print(f"数据库迁移失败: {e}")
        return False

if __name__ == "__main__":
    success = migrate_database()
    sys.exit(0 if success else 1)
