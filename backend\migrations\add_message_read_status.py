#!/usr/bin/env python3
"""
数据库迁移脚本：添加 message_read_status 表来追踪消息已读状态
"""

import sqlite3
import os
import sys

def migrate_database():
    """执行数据库迁移"""
    # 获取数据库文件路径
    db_path = os.path.join(os.path.dirname(__file__), '..', 'instance', 'chat_dev.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查 message_read_status 表是否已存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='message_read_status'")
        table_exists = cursor.fetchone() is not None
        
        if not table_exists:
            print("创建 message_read_status 表...")
            cursor.execute("""
                CREATE TABLE message_read_status (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    message_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    read_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (message_id) REFERENCES messages (id),
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    UNIQUE (message_id, user_id)
                )
            """)
            print("✓ message_read_status 表创建完成")
        else:
            print("message_read_status 表已存在，跳过")
        
        conn.commit()
        conn.close()
        
        print("数据库迁移完成！")
        return True
        
    except Exception as e:
        print(f"数据库迁移失败: {e}")
        return False

if __name__ == "__main__":
    success = migrate_database()
    sys.exit(0 if success else 1)
