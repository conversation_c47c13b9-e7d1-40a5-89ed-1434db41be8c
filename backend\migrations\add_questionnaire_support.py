#!/usr/bin/env python3
"""
数据库迁移脚本：添加问卷功能支持
"""

import sqlite3
import os
import sys

def add_questionnaire_support():
    """添加问卷功能相关的数据库表"""
    
    # 获取数据库路径
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'chat.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("开始添加问卷功能支持...")
        
        # 1. 创建问卷表
        print("创建 questionnaires 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS questionnaires (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title VARCHAR(200) NOT NULL,
                description TEXT,
                creator_id INTEGER NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (creator_id) REFERENCES users (id)
            )
        """)
        
        # 2. 创建问卷页面表
        print("创建 questionnaire_pages 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS questionnaire_pages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                questionnaire_id INTEGER NOT NULL,
                title VARCHAR(200),
                description TEXT,
                page_order INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (questionnaire_id) REFERENCES questionnaires (id) ON DELETE CASCADE
            )
        """)
        
        # 3. 创建题目表
        print("创建 questions 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS questions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                page_id INTEGER NOT NULL,
                question_text TEXT NOT NULL,
                question_type VARCHAR(20) NOT NULL,
                is_required BOOLEAN DEFAULT 0,
                question_order INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (page_id) REFERENCES questionnaire_pages (id) ON DELETE CASCADE
            )
        """)
        
        # 4. 创建题目选项表
        print("创建 question_options 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS question_options (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                question_id INTEGER NOT NULL,
                option_text VARCHAR(500) NOT NULL,
                option_order INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (question_id) REFERENCES questions (id) ON DELETE CASCADE
            )
        """)
        
        # 5. 创建问卷回答表
        print("创建 questionnaire_responses 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS questionnaire_responses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                questionnaire_id INTEGER NOT NULL,
                respondent_id INTEGER NOT NULL,
                sender_id INTEGER NOT NULL,
                message_id INTEGER,
                is_completed BOOLEAN DEFAULT 0,
                submitted_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (questionnaire_id) REFERENCES questionnaires (id),
                FOREIGN KEY (respondent_id) REFERENCES users (id),
                FOREIGN KEY (sender_id) REFERENCES users (id),
                FOREIGN KEY (message_id) REFERENCES messages (id)
            )
        """)
        
        # 6. 创建回答表
        print("创建 answers 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS answers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                response_id INTEGER NOT NULL,
                question_id INTEGER NOT NULL,
                selected_option_ids TEXT,
                text_answer TEXT,
                is_hidden BOOLEAN DEFAULT 0,
                is_revealed BOOLEAN DEFAULT 0,
                revealed_at DATETIME,
                revealed_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (response_id) REFERENCES questionnaire_responses (id) ON DELETE CASCADE,
                FOREIGN KEY (question_id) REFERENCES questions (id),
                FOREIGN KEY (revealed_by) REFERENCES users (id)
            )
        """)
        
        # 7. 创建揭秘申请表
        print("创建 reveal_requests 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS reveal_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                answer_id INTEGER NOT NULL,
                requester_id INTEGER NOT NULL,
                status VARCHAR(20) DEFAULT 'pending',
                requested_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                processed_at DATETIME,
                FOREIGN KEY (answer_id) REFERENCES answers (id) ON DELETE CASCADE,
                FOREIGN KEY (requester_id) REFERENCES users (id)
            )
        """)
        
        # 8. 检查 messages 表是否需要添加 questionnaire_id 字段
        cursor.execute("PRAGMA table_info(messages)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'questionnaire_id' not in columns:
            print("添加 questionnaire_id 字段到 messages 表...")
            cursor.execute("ALTER TABLE messages ADD COLUMN questionnaire_id INTEGER")
            print("✓ messages 表 questionnaire_id 字段添加完成")
        else:
            print("messages 表已有 questionnaire_id 字段，跳过")
        
        # 9. 创建索引以提高查询性能
        print("创建索引...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_questionnaires_creator ON questionnaires(creator_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_questionnaire_pages_questionnaire ON questionnaire_pages(questionnaire_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_questions_page ON questions(page_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_question_options_question ON question_options(question_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_questionnaire ON questionnaire_responses(questionnaire_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_respondent ON questionnaire_responses(respondent_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_answers_response ON answers(response_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_answers_question ON answers(question_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_reveal_requests_answer ON reveal_requests(answer_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_reveal_requests_requester ON reveal_requests(requester_id)")
        
        conn.commit()
        print("✓ 问卷功能数据库迁移完成")
        
        return True
        
    except Exception as e:
        print(f"迁移失败: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    success = add_questionnaire_support()
    if success:
        print("数据库迁移成功完成！")
        sys.exit(0)
    else:
        print("数据库迁移失败！")
        sys.exit(1)
