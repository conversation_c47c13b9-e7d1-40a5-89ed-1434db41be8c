#!/usr/bin/env python3
"""
数据库迁移脚本：添加揭秘申请支持
添加 reveal_request_id 列到 messages 表
"""

import sqlite3
import os
import sys

def migrate_database():
    """执行数据库迁移"""
    
    # 数据库文件路径
    db_path = os.path.join('instance', 'chat.db')
    
    if not os.path.exists(db_path):
        print(f"错误：数据库文件不存在 {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("开始数据库迁移...")
        
        # 检查 messages 表的当前结构
        cursor.execute("PRAGMA table_info(messages)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"当前 messages 表的列: {column_names}")
        
        # 检查 reveal_request_id 列是否已存在
        if 'reveal_request_id' in column_names:
            print("reveal_request_id 列已存在，无需迁移")
            conn.close()
            return True
        
        # 添加 reveal_request_id 列
        print("添加 reveal_request_id 列...")
        cursor.execute("ALTER TABLE messages ADD COLUMN reveal_request_id INTEGER")
        
        # 提交更改
        conn.commit()
        
        # 验证列已添加
        cursor.execute("PRAGMA table_info(messages)")
        new_columns = cursor.fetchall()
        new_column_names = [col[1] for col in new_columns]
        
        if 'reveal_request_id' in new_column_names:
            print("✓ reveal_request_id 列添加成功")
            print(f"更新后的 messages 表的列: {new_column_names}")
        else:
            print("✗ reveal_request_id 列添加失败")
            conn.close()
            return False
        
        # 关闭连接
        conn.close()
        
        print("数据库迁移完成！")
        return True
        
    except sqlite3.Error as e:
        print(f"数据库错误: {e}")
        return False
    except Exception as e:
        print(f"迁移过程中发生错误: {e}")
        return False

def rollback_migration():
    """回滚迁移（SQLite 不支持删除列，所以这里只是提示）"""
    print("警告：SQLite 不支持删除列。")
    print("如需回滚，请手动备份数据并重新创建表。")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'rollback':
        rollback_migration()
    else:
        success = migrate_database()
        if success:
            print("\n迁移成功！现在可以启用揭秘申请功能。")
        else:
            print("\n迁移失败！请检查错误信息。")
            sys.exit(1)
