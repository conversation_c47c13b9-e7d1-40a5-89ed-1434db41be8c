"""
数据库迁移脚本：添加陌生人消息设置和好友申请拒绝次数记录
"""

import os
import sys
import sqlite3

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def get_db_path():
    """获取数据库文件路径"""
    # 数据库文件在backend/instance目录下
    backend_dir = os.path.dirname(os.path.abspath(__file__))
    instance_dir = os.path.join(backend_dir, 'instance')

    # 尝试多个可能的数据库文件名
    possible_names = ['chat_dev.db', 'chat.db', 'database.db']

    for db_name in possible_names:
        db_path = os.path.join(instance_dir, db_name)
        if os.path.exists(db_path):
            return db_path

    # 如果都不存在，返回默认路径
    return os.path.join(instance_dir, 'chat_dev.db')

def upgrade():
    """执行升级迁移"""
    db_path = get_db_path()

    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # 1. 检查 allow_stranger_messages 字段是否已存在
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'allow_stranger_messages' not in columns:
            # 添加 allow_stranger_messages 字段到 users 表
            cursor.execute("""
                ALTER TABLE users
                ADD COLUMN allow_stranger_messages BOOLEAN NOT NULL DEFAULT 0
            """)
            print("✅ Added allow_stranger_messages column to users table")
        else:
            print("ℹ️ allow_stranger_messages column already exists")

        # 2. 检查 friend_request_reject_counts 表是否已存在
        cursor.execute("""
            SELECT name FROM sqlite_master
            WHERE type='table' AND name='friend_request_reject_counts'
        """)

        if not cursor.fetchone():
            # 创建 friend_request_reject_counts 表
            cursor.execute("""
                CREATE TABLE friend_request_reject_counts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sender_id INTEGER NOT NULL,
                    receiver_id INTEGER NOT NULL,
                    reject_count INTEGER NOT NULL DEFAULT 0,
                    last_rejected_at DATETIME,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (sender_id) REFERENCES users (id),
                    FOREIGN KEY (receiver_id) REFERENCES users (id),
                    UNIQUE (sender_id, receiver_id)
                )
            """)
            print("✅ Created friend_request_reject_counts table")
        else:
            print("ℹ️ friend_request_reject_counts table already exists")

        conn.commit()
        print("✅ Migration completed successfully!")

    except Exception as e:
        print(f"❌ Migration failed: {e}")
        conn.rollback()
    finally:
        conn.close()

def downgrade():
    """执行降级迁移"""
    db_path = get_db_path()

    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # 1. 删除 friend_request_reject_counts 表
        cursor.execute("DROP TABLE IF EXISTS friend_request_reject_counts")
        print("✅ Dropped friend_request_reject_counts table")

        # 2. 删除 allow_stranger_messages 字段
        # SQLite 不支持直接删除列，需要重建表
        cursor.execute("""
            CREATE TABLE users_backup AS
            SELECT id, username, email, display_name, password_hash, is_anonymous,
                   cookie_id, banned, muted, is_admin, can_create_groups,
                   privacy_setting, avatar, created_at, last_seen
            FROM users
        """)

        cursor.execute("DROP TABLE users")
        cursor.execute("ALTER TABLE users_backup RENAME TO users")

        print("✅ Removed allow_stranger_messages column from users table")

        conn.commit()
        print("✅ Downgrade completed successfully!")

    except Exception as e:
        print(f"❌ Downgrade failed: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Database migration script')
    parser.add_argument('--downgrade', action='store_true', help='Run downgrade migration')
    args = parser.parse_args()

    if args.downgrade:
        print("Running database downgrade...")
        downgrade()
    else:
        print("Running database upgrade...")
        upgrade()

    print("Migration completed!")
