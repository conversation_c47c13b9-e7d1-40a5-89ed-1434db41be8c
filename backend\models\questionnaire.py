from datetime import datetime
import enum

# 导入db实例
try:
    from extensions import db
    from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Enum
    from sqlalchemy.orm import relationship
except ImportError:
    # 如果extensions不可用，使用models.py中的db
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from models import db
    Column = db.Column
    Integer = db.Integer
    String = db.String
    Text = db.Text
    Boolean = db.Boolean
    DateTime = db.DateTime
    ForeignKey = db.ForeignKey
    relationship = db.relationship

    # 定义Enum
    class Enum:
        pass

class QuestionType(enum.Enum):
    SINGLE_CHOICE = "single_choice"  # 单选
    MULTIPLE_CHOICE = "multiple_choice"  # 多选
    TEXT_INPUT = "text_input"  # 手动输入

class Questionnaire(db.Model):
    """问卷表"""
    __tablename__ = 'questionnaires'
    
    id = Column(Integer, primary_key=True)
    title = Column(String(200), nullable=False, comment='问卷标题')
    description = Column(Text, comment='问卷描述')
    creator_id = Column(Integer, ForeignKey('users.id'), nullable=False, comment='创建者ID')
    is_active = Column(Boolean, default=True, comment='是否启用')
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    creator = relationship("User", backref="created_questionnaires")
    pages = relationship("QuestionnairePage", back_populates="questionnaire", cascade="all, delete-orphan", order_by="QuestionnairePage.page_order")
    responses = relationship("QuestionnaireResponse", back_populates="questionnaire", cascade="all, delete-orphan")
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'creator_id': self.creator_id,
            'creator_username': self.creator.username if self.creator else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'pages': [page.to_dict() for page in self.pages] if self.pages else []
        }

class QuestionnairePage(db.Model):
    """问卷页面表"""
    __tablename__ = 'questionnaire_pages'
    
    id = Column(Integer, primary_key=True)
    questionnaire_id = Column(Integer, ForeignKey('questionnaires.id'), nullable=False, comment='问卷ID')
    title = Column(String(200), comment='页面标题')
    description = Column(Text, comment='页面描述')
    page_order = Column(Integer, nullable=False, comment='页面顺序')
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 关系
    questionnaire = relationship("Questionnaire", back_populates="pages")
    questions = relationship("Question", back_populates="page", cascade="all, delete-orphan", order_by="Question.question_order")
    
    def to_dict(self):
        return {
            'id': self.id,
            'questionnaire_id': self.questionnaire_id,
            'title': self.title,
            'description': self.description,
            'page_order': self.page_order,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'questions': [question.to_dict() for question in self.questions] if self.questions else []
        }

class Question(db.Model):
    """题目表"""
    __tablename__ = 'questions'
    
    id = Column(Integer, primary_key=True)
    page_id = Column(Integer, ForeignKey('questionnaire_pages.id'), nullable=False, comment='页面ID')
    question_text = Column(Text, nullable=False, comment='题目内容')
    question_type = Column(Enum(QuestionType), nullable=False, comment='题目类型')
    is_required = Column(Boolean, default=False, comment='是否必填')
    question_order = Column(Integer, nullable=False, comment='题目顺序')
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 关系
    page = relationship("QuestionnairePage", back_populates="questions")
    options = relationship("QuestionOption", back_populates="question", cascade="all, delete-orphan", order_by="QuestionOption.option_order")
    answers = relationship("Answer", back_populates="question", cascade="all, delete-orphan")
    
    def to_dict(self):
        return {
            'id': self.id,
            'page_id': self.page_id,
            'question_text': self.question_text,
            'question_type': self.question_type.value if self.question_type else None,
            'is_required': self.is_required,
            'question_order': self.question_order,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'options': [option.to_dict() for option in self.options] if self.options else []
        }

class QuestionOption(db.Model):
    """题目选项表"""
    __tablename__ = 'question_options'
    
    id = Column(Integer, primary_key=True)
    question_id = Column(Integer, ForeignKey('questions.id'), nullable=False, comment='题目ID')
    option_text = Column(String(500), nullable=False, comment='选项内容')
    option_order = Column(Integer, nullable=False, comment='选项顺序')
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 关系
    question = relationship("Question", back_populates="options")
    
    def to_dict(self):
        return {
            'id': self.id,
            'question_id': self.question_id,
            'option_text': self.option_text,
            'option_order': self.option_order,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class QuestionnaireResponse(db.Model):
    """问卷回答表"""
    __tablename__ = 'questionnaire_responses'
    
    id = Column(Integer, primary_key=True)
    questionnaire_id = Column(Integer, ForeignKey('questionnaires.id'), nullable=False, comment='问卷ID')
    respondent_id = Column(Integer, ForeignKey('users.id'), nullable=False, comment='回答者ID')
    sender_id = Column(Integer, ForeignKey('users.id'), nullable=False, comment='发送者ID')
    message_id = Column(Integer, ForeignKey('messages.id'), comment='关联的消息ID')
    is_completed = Column(Boolean, default=False, comment='是否完成')
    submitted_at = Column(DateTime, comment='提交时间')
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 关系
    questionnaire = relationship("Questionnaire", back_populates="responses")
    respondent = relationship("User", foreign_keys=[respondent_id], backref="questionnaire_responses")
    sender = relationship("User", foreign_keys=[sender_id], backref="sent_questionnaires")
    message = relationship("Message", backref="questionnaire_response")
    answers = relationship("Answer", back_populates="response", cascade="all, delete-orphan")
    
    def to_dict(self):
        return {
            'id': self.id,
            'questionnaire_id': self.questionnaire_id,
            'respondent_id': self.respondent_id,
            'sender_id': self.sender_id,
            'message_id': self.message_id,
            'is_completed': self.is_completed,
            'submitted_at': self.submitted_at.isoformat() if self.submitted_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'questionnaire': self.questionnaire.to_dict() if self.questionnaire else None,
            'answers': [answer.to_dict() for answer in self.answers] if self.answers else []
        }

class Answer(db.Model):
    """回答表"""
    __tablename__ = 'answers'
    
    id = Column(Integer, primary_key=True)
    response_id = Column(Integer, ForeignKey('questionnaire_responses.id'), nullable=False, comment='回答记录ID')
    question_id = Column(Integer, ForeignKey('questions.id'), nullable=False, comment='题目ID')
    selected_option_ids = Column(Text, comment='选中的选项ID列表(JSON格式)')
    text_answer = Column(Text, comment='文本回答')
    is_hidden = Column(Boolean, default=False, comment='是否隐藏')
    is_revealed = Column(Boolean, default=False, comment='是否已揭秘')
    revealed_at = Column(DateTime, comment='揭秘时间')
    revealed_by = Column(Integer, ForeignKey('users.id'), comment='揭秘者ID')
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 关系
    response = relationship("QuestionnaireResponse", back_populates="answers")
    question = relationship("Question", back_populates="answers")
    revealer = relationship("User", foreign_keys=[revealed_by], backref="revealed_answers")
    
    def to_dict(self):
        import json
        selected_options = []
        if self.selected_option_ids:
            try:
                option_ids = json.loads(self.selected_option_ids)
                selected_options = [opt.to_dict() for opt in self.question.options if opt.id in option_ids]
            except:
                pass
        
        return {
            'id': self.id,
            'response_id': self.response_id,
            'question_id': self.question_id,
            'selected_option_ids': self.selected_option_ids,
            'selected_options': selected_options,
            'text_answer': self.text_answer,
            'is_hidden': self.is_hidden,
            'is_revealed': self.is_revealed,
            'revealed_at': self.revealed_at.isoformat() if self.revealed_at else None,
            'revealed_by': self.revealed_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'question': self.question.to_dict() if self.question else None
        }


