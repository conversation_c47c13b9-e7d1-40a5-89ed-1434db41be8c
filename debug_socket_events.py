#!/usr/bin/env python3
"""
调试Socket事件发送
"""

import socketio
import requests
import time
import json

# 配置
BACKEND_URL = "http://localhost:5000"
API_URL = f"{BACKEND_URL}/api"

def test_socket_events():
    """测试Socket事件接收"""
    
    print("🔧 开始Socket事件调试...")
    
    # 创建Socket.IO客户端
    sio = socketio.Client()
    
    # 事件监听器
    @sio.event
    def connect():
        print("✅ Socket.IO客户端已连接")
    
    @sio.event
    def disconnect():
        print("❌ Socket.IO客户端已断开")
    
    @sio.event
    def friend_request_received(data):
        print(f"🔔 收到好友请求事件: {json.dumps(data, indent=2)}")
    
    @sio.event
    def connected(data):
        print(f"🔌 收到连接确认事件: {json.dumps(data, indent=2)}")
    
    @sio.event
    def error(data):
        print(f"❌ 收到错误事件: {json.dumps(data, indent=2)}")
    
    try:
        # 连接到Socket.IO服务器
        print("🔌 连接到Socket.IO服务器...")
        sio.connect(BACKEND_URL)
        
        # 等待连接稳定
        time.sleep(2)
        
        # 模拟发送好友请求
        print("\n📤 模拟发送好友请求...")
        
        # 1. 注册测试用户
        user_data = {
            "username": f"socket_test_{int(time.time())}",
            "email": f"socket_test_{int(time.time())}@test.com",
            "password": "password123"
        }
        
        register_response = requests.post(f"{API_URL}/auth/register", json=user_data)
        if register_response.status_code == 201:
            print(f"✅ 测试用户注册成功")
            
            # 2. 登录获取cookies
            login_response = requests.post(f"{API_URL}/auth/login", json={
                "username": user_data["username"],
                "password": user_data["password"]
            })
            
            if login_response.status_code == 200:
                cookies = login_response.cookies
                print(f"✅ 登录成功")
                
                # 3. 搜索现有用户
                search_response = requests.get(f"{API_URL}/friends/search", 
                                             params={"q": "test"}, 
                                             cookies=cookies)
                
                if search_response.status_code == 200:
                    users = search_response.json().get('users', [])
                    if users:
                        receiver_id = users[0]['id']
                        print(f"✅ 找到接收者用户 ID: {receiver_id}")
                        
                        # 4. 发送好友请求
                        friend_request_data = {
                            "receiver_id": receiver_id,
                            "message": "Socket调试测试好友请求"
                        }
                        
                        print(f"📤 发送好友请求到用户 {receiver_id}...")
                        request_response = requests.post(f"{API_URL}/friends/requests", 
                                                       json=friend_request_data,
                                                       cookies=cookies)
                        
                        print(f"📤 好友请求响应状态: {request_response.status_code}")
                        if request_response.status_code == 201:
                            print(f"✅ 好友请求发送成功")
                            print(f"📄 响应内容: {request_response.json()}")
                        else:
                            print(f"❌ 好友请求发送失败: {request_response.text}")
                    else:
                        print("❌ 未找到可发送请求的用户")
                else:
                    print(f"❌ 搜索用户失败: {search_response.text}")
            else:
                print(f"❌ 登录失败: {login_response.text}")
        else:
            print(f"❌ 用户注册失败: {register_response.text}")
        
        # 等待接收事件
        print("\n⏳ 等待接收Socket事件...")
        time.sleep(5)
        
    except Exception as e:
        print(f"❌ Socket测试异常: {e}")
    finally:
        # 断开连接
        if sio.connected:
            sio.disconnect()
            print("🔌 Socket.IO客户端已断开")

if __name__ == "__main__":
    test_socket_events()
