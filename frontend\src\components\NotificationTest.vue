<template>
  <div class="p-4 bg-gray-100 rounded-lg">
    <h3 class="text-lg font-medium mb-4">通知测试</h3>
    
    <div class="space-y-4">
      <!-- 浏览器通知测试 -->
      <div>
        <h4 class="font-medium mb-2">浏览器通知测试</h4>
        <div class="space-x-2">
          <button 
            @click="testBrowserNotification"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            测试浏览器通知
          </button>
          <button 
            @click="requestNotificationPermission"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            请求通知权限
          </button>
        </div>
        <p class="text-sm text-gray-600 mt-2">
          当前权限状态: {{ notificationPermission }}
        </p>
      </div>

      <!-- 通知Store测试 -->
      <div>
        <h4 class="font-medium mb-2">通知Store测试</h4>
        <div class="space-x-2">
          <button 
            @click="addTestFriendRequest"
            class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            添加测试好友请求
          </button>
          <button
            @click="clearNotifications"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            清空通知
          </button>
          <button
            @click="testToastNotification"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            测试Toast通知
          </button>
          <button
            @click="testAudioNotification"
            class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
          >
            测试音效通知
          </button>
        </div>
        <div class="mt-2 text-sm">
          <p>好友请求数量: {{ notificationsStore.friendRequests.length }}</p>
          <p>未读通知数量: {{ notificationsStore.unreadCount }}</p>
          <p>总未读数量: {{ notificationsStore.totalUnreadCount }}</p>
        </div>
      </div>

      <!-- Socket.IO测试 -->
      <div>
        <h4 class="font-medium mb-2">Socket.IO测试</h4>
        <div class="space-x-2">
          <button 
            @click="testSocketConnection"
            class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
          >
            测试Socket连接
          </button>
        </div>
        <p class="text-sm text-gray-600 mt-2">
          Socket状态: {{ socketConnected ? '已连接' : '未连接' }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useNotificationsStore } from '@/stores/notifications'
import { notificationService } from '@/services/notificationService'
import { socketService } from '@/services/socket'
import { toastService } from '@/services/toastService'
import { audioService } from '@/services/audioService'
import type { FriendRequest } from '@/types'

const notificationsStore = useNotificationsStore()
const notificationPermission = ref<NotificationPermission>('default')
const socketConnected = ref(false)

onMounted(() => {
  notificationPermission.value = notificationService.getPermission()
  socketConnected.value = socketService.isConnected()
})

const testBrowserNotification = async () => {
  console.log('Testing browser notification...')
  await notificationService.showFriendRequestNotification('测试用户')
}

const requestNotificationPermission = async () => {
  console.log('Requesting notification permission...')
  notificationPermission.value = await notificationService.requestPermission()
}

const addTestFriendRequest = () => {
  console.log('Adding test friend request...')
  const testRequest: FriendRequest = {
    id: Date.now(),
    sender_id: 999,
    sender_username: '测试用户',
    sender_display_name: '测试用户',
    receiver_id: 1,
    receiver_username: '当前用户',
    receiver_display_name: '当前用户',
    status: 'pending',
    message: '这是一个测试好友请求',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  
  notificationsStore.addFriendRequest(testRequest)
}

const clearNotifications = () => {
  console.log('Clearing notifications...')
  notificationsStore.clearAllNotifications()
  notificationsStore.clearFriendRequests()
}

const testSocketConnection = () => {
  console.log('Testing socket connection...')
  socketConnected.value = socketService.isConnected()
  console.log('Socket connected:', socketConnected.value)
}

const testToastNotification = () => {
  console.log('Testing toast notification...')
  toastService.showFriendRequestToast('测试用户Toast')
}

const testAudioNotification = async () => {
  console.log('Testing audio notification...')
  await audioService.playNotificationSound()
}
</script>
