<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 translate-x-full"
      enter-to-class="opacity-100 translate-x-0"
      leave-active-class="transition-all duration-300 ease-in"
      leave-from-class="opacity-100 translate-x-0"
      leave-to-class="opacity-0 translate-x-full"
    >
      <div
        v-if="visible"
        class="fixed top-4 right-4 z-50 max-w-sm bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden"
        @click="handleClick"
      >
        <!-- 进度条 -->
        <div class="absolute top-0 left-0 h-1 bg-blue-500 transition-all duration-100 ease-linear" :style="{ width: progressWidth + '%' }"></div>
        
        <div class="p-4">
          <div class="flex items-start space-x-3">
            <!-- 图标 -->
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-2.239"></path>
                </svg>
              </div>
            </div>
            
            <!-- 内容 -->
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900">
                新的好友请求
              </p>
              <p class="text-sm text-gray-600 mt-1">
                {{ message }}
              </p>
            </div>
            
            <!-- 关闭按钮 -->
            <button
              @click.stop="hide"
              class="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <!-- 操作按钮 -->
          <div class="mt-3 flex space-x-2">
            <button
              @click.stop="handleViewRequest"
              class="flex-1 bg-blue-600 text-white text-sm px-3 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              查看请求
            </button>
            <button
              @click.stop="hide"
              class="flex-1 bg-gray-100 text-gray-700 text-sm px-3 py-2 rounded-md hover:bg-gray-200 transition-colors"
            >
              稍后处理
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

interface Props {
  message: string
  duration?: number
}

const props = withDefaults(defineProps<Props>(), {
  duration: 5000
})

const emit = defineEmits<{
  close: []
}>()

const router = useRouter()
const visible = ref(false)
const progressWidth = ref(100)
let timer: number | null = null
let progressTimer: number | null = null

const show = () => {
  visible.value = true
  startProgress()
}

const hide = () => {
  visible.value = false
  clearTimers()
  emit('close')
}

const handleClick = () => {
  handleViewRequest()
}

const handleViewRequest = () => {
  router.push('/friends')
  hide()
}

const startProgress = () => {
  if (props.duration <= 0) return
  
  const interval = 50 // 更新间隔（毫秒）
  const steps = props.duration / interval
  const stepSize = 100 / steps
  
  progressTimer = window.setInterval(() => {
    progressWidth.value -= stepSize
    if (progressWidth.value <= 0) {
      hide()
    }
  }, interval)
  
  timer = window.setTimeout(() => {
    hide()
  }, props.duration)
}

const clearTimers = () => {
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimer = null
  }
}

onMounted(() => {
  show()
})

onUnmounted(() => {
  clearTimers()
})

// 暴露方法给父组件
defineExpose({
  show,
  hide
})
</script>

<style scoped>
/* 确保Toast在最顶层 */
.fixed {
  z-index: 9999;
}
</style>
