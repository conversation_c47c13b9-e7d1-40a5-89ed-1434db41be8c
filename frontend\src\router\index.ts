import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 路由组件懒加载
const Home = () => import('@/views/Home.vue')
const Chat = () => import('@/views/Chat.vue')
const Login = () => import('@/views/Login.vue')
const Register = () => import('@/views/Register.vue')
const ChatLink = () => import('@/views/ChatLink.vue')
const InviteCode = () => import('@/views/InviteCode.vue')
const Admin = () => import('@/views/Admin.vue')
const NotFound = () => import('@/views/NotFound.vue')
const NotificationTest = () => import('@/components/NotificationTest.vue')
const NotificationDemo = () => import('@/views/NotificationDemo.vue')
const SimpleNotificationTest = () => import('@/views/SimpleNotificationTest.vue')
const TestPage = () => import('@/views/TestPage.vue')
const NotificationIconTest = () => import('@/views/NotificationIconTest.vue')
const SocketDebug = () => import('@/views/SocketDebug.vue')

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home,
      meta: { requiresAuth: false }
    },
    {
      path: '/login',
      name: 'login',
      component: Login,
      meta: { requiresAuth: false, hideForAuth: true }
    },
    {
      path: '/register',
      name: 'register',
      component: Register,
      meta: { requiresAuth: false, hideForAuth: true }
    },
    {
      path: '/chat/:userId?',
      name: 'chat',
      component: Chat,
      meta: { requiresAuth: true },
      props: route => ({ userId: route.params.userId ? Number(route.params.userId) : null })
    },
    {
      path: '/chat-link/:code',
      name: 'chatLink',
      component: ChatLink,
      meta: { requiresAuth: false },
      props: true
    },
    {
      path: '/invite/:code',
      name: 'inviteCode',
      component: InviteCode,
      meta: { requiresAuth: false },
      props: true
    },
    {
      path: '/admin',
      name: 'admin',
      component: Admin,
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/friends',
      name: 'friends',
      component: () => import('../views/Friends.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/group-chat/:groupId?',
      name: 'groupChat',
      component: () => import('../views/GroupChat.vue'),
      meta: { requiresAuth: true },
      props: route => ({ groupId: route.params.groupId ? Number(route.params.groupId) : null })
    },
    {
      path: '/questionnaire/:questionnaireId/:messageId/:senderId',
      name: 'questionnaire-answer',
      component: () => import('../views/QuestionnaireAnswer.vue'),
      meta: { requiresAuth: true },
      props: true
    },
    {
      path: '/questionnaire-response/:responseId',
      name: 'questionnaire-response-detail',
      component: () => import('../views/QuestionnaireResponseDetail.vue'),
      meta: { requiresAuth: true },
      props: true
    },
    {
      path: '/notification-test',
      name: 'notification-test',
      component: NotificationTest,
      meta: { requiresAuth: false }
    },
    {
      path: '/notification-demo',
      name: 'notification-demo',
      component: NotificationDemo,
      meta: { requiresAuth: false }
    },
    {
      path: '/simple-test',
      name: 'simple-test',
      component: SimpleNotificationTest,
      meta: { requiresAuth: false }
    },
    {
      path: '/test',
      name: 'test',
      component: TestPage,
      meta: { requiresAuth: false }
    },
    {
      path: '/icon-test',
      name: 'icon-test',
      component: NotificationIconTest,
      meta: { requiresAuth: false }
    },
    {
      path: '/socket-debug',
      name: 'socket-debug',
      component: SocketDebug,
      meta: { requiresAuth: false }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'notFound',
      component: NotFound
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 检查是否是好友邀请链接或邀请码访问
  const isFriendLinkAccess = to.name === 'chatLink' && to.params.code
  const isInviteCodeAccess = to.name === 'inviteCode' && to.params.code

  // 如果用户未初始化，先初始化认证状态（只初始化一次）
  if (!authStore.initialized) {
    try {
      console.log('Initializing auth state in router guard...')
      await authStore.initAuth()
    } catch (error) {
      console.error('Failed to initialize auth in router guard:', error)
      // 即使初始化失败，也继续路由导航，让组件处理未认证状态
    }
  }

  const requiresAuth = to.meta.requiresAuth
  const requiresAdmin = to.meta.requiresAdmin
  const hideForAuth = to.meta.hideForAuth
  const isAuthenticated = authStore.isAuthenticated
  const isAdmin = authStore.isAdmin

  // 特殊处理：好友链接或邀请码访问且用户未登录时，允许访问（将在组件中自动创建匿名用户）
  if ((isFriendLinkAccess || isInviteCodeAccess) && !isAuthenticated) {
    next()
    return
  }

  // 如果页面需要认证但用户未登录
  if (requiresAuth && !isAuthenticated) {
    next({ name: 'login', query: { redirect: to.fullPath } })
    return
  }

  // 如果页面需要管理员权限但用户不是管理员
  if (requiresAdmin && !isAdmin) {
    next({ name: 'home' })
    return
  }

  // 如果用户已登录但访问登录/注册页面，重定向到聊天页面
  if (hideForAuth && isAuthenticated) {
    next({ name: 'chat' })
    return
  }

  // 如果已登录用户访问首页，重定向到聊天页面
  if (to.name === 'home' && isAuthenticated) {
    next({ name: 'chat' })
    return
  }

  next()
})

export default router
