/**
 * 音频通知服务
 */
export class AudioService {
  private static instance: AudioService
  private audioContext: AudioContext | null = null
  private enabled: boolean = true

  private constructor() {
    this.initAudioContext()
  }

  public static getInstance(): AudioService {
    if (!AudioService.instance) {
      AudioService.instance = new AudioService()
    }
    return AudioService.instance
  }

  /**
   * 初始化音频上下文
   */
  private initAudioContext(): void {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
    } catch (error) {
      console.warn('Audio context not supported:', error)
    }
  }

  /**
   * 播放通知音效
   */
  public async playNotificationSound(): Promise<void> {
    if (!this.enabled || !this.audioContext) {
      return
    }

    try {
      // 确保音频上下文处于运行状态
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }

      // 创建一个简单的通知音效
      const oscillator = this.audioContext.createOscillator()
      const gainNode = this.audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(this.audioContext.destination)

      // 设置音效参数
      oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime)
      oscillator.frequency.setValueAtTime(600, this.audioContext.currentTime + 0.1)
      
      gainNode.gain.setValueAtTime(0, this.audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(0.1, this.audioContext.currentTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.3)

      oscillator.start(this.audioContext.currentTime)
      oscillator.stop(this.audioContext.currentTime + 0.3)

    } catch (error) {
      console.warn('Failed to play notification sound:', error)
    }
  }

  /**
   * 启用/禁用音效
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled
  }

  /**
   * 检查是否启用音效
   */
  public isEnabled(): boolean {
    return this.enabled
  }
}

export const audioService = AudioService.getInstance()
