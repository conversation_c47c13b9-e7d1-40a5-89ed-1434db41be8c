/**
 * 浏览器原生通知服务
 */
export class NotificationService {
  private static instance: NotificationService
  private permission: NotificationPermission = 'default'

  private constructor() {
    this.checkPermission()
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService()
    }
    return NotificationService.instance
  }

  /**
   * 检查通知权限
   */
  private checkPermission(): void {
    if ('Notification' in window) {
      this.permission = Notification.permission
    }
  }

  /**
   * 请求通知权限
   */
  public async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications')
      return 'denied'
    }

    if (this.permission === 'default') {
      this.permission = await Notification.requestPermission()
    }

    return this.permission
  }

  /**
   * 显示通知
   */
  public async showNotification(title: string, options?: NotificationOptions): Promise<void> {
    console.log('Attempting to show notification:', title)

    // 检查浏览器支持
    if (!this.isSupported()) {
      console.warn('Browser does not support notifications')
      return
    }

    // 确保有权限
    if (this.permission !== 'granted') {
      console.log('Requesting notification permission...')
      const permission = await this.requestPermission()
      if (permission !== 'granted') {
        console.warn('Notification permission denied:', permission)
        return
      }
    }

    try {
      // 创建通知
      const notification = new Notification(title, {
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        ...options
      })

      console.log('Notification created successfully')

      // 自动关闭通知（5秒后）
      setTimeout(() => {
        notification.close()
      }, 5000)

      // 点击通知时聚焦窗口
      notification.onclick = () => {
        window.focus()
        notification.close()
      }

      // 添加错误处理
      notification.onerror = (error) => {
        console.error('Notification error:', error)
      }

    } catch (error) {
      console.error('Failed to create notification:', error)
    }
  }

  /**
   * 显示好友请求通知
   */
  public async showFriendRequestNotification(senderName: string): Promise<void> {
    // 如果没有权限，先尝试请求权限
    if (this.permission === 'default') {
      await this.requestPermission()
    }

    await this.showNotification(`${senderName} 申请添加您为好友`, {
      body: '点击查看详情',
      tag: 'friend-request',
      requireInteraction: false,
      icon: '/favicon.ico'
    })
  }

  /**
   * 检查是否支持通知
   */
  public isSupported(): boolean {
    return 'Notification' in window
  }

  /**
   * 获取当前权限状态
   */
  public getPermission(): NotificationPermission {
    return this.permission
  }
}

export const notificationService = NotificationService.getInstance()
