import { createApp, type App } from 'vue'
import NotificationToast from '@/components/NotificationToast.vue'

interface ToastOptions {
  message: string
  duration?: number
}

/**
 * Toast通知服务
 */
export class ToastService {
  private static instance: ToastService
  private toasts: App[] = []

  private constructor() {}

  public static getInstance(): ToastService {
    if (!ToastService.instance) {
      ToastService.instance = new ToastService()
    }
    return ToastService.instance
  }

  /**
   * 显示好友请求Toast
   */
  public showFriendRequestToast(senderName: string, duration: number = 5000): void {
    const message = `${senderName} 申请添加您为好友`
    this.showToast({ message, duration })
  }

  /**
   * 显示Toast通知
   */
  private showToast(options: ToastOptions): void {
    // 创建容器元素
    const container = document.createElement('div')
    document.body.appendChild(container)

    // 创建Vue应用实例
    const app = createApp(NotificationToast, {
      ...options,
      onClose: () => {
        // 清理
        this.cleanup(app, container)
      }
    })

    // 挂载应用
    app.mount(container)
    
    // 保存引用
    this.toasts.push(app)
  }

  /**
   * 清理Toast
   */
  private cleanup(app: App, container: HTMLElement): void {
    // 从数组中移除
    const index = this.toasts.indexOf(app)
    if (index > -1) {
      this.toasts.splice(index, 1)
    }

    // 卸载应用
    app.unmount()
    
    // 移除容器
    if (container.parentNode) {
      container.parentNode.removeChild(container)
    }
  }

  /**
   * 清理所有Toast
   */
  public clearAll(): void {
    this.toasts.forEach(app => {
      app.unmount()
    })
    this.toasts = []
    
    // 清理所有可能残留的容器
    const containers = document.querySelectorAll('[data-toast-container]')
    containers.forEach(container => {
      if (container.parentNode) {
        container.parentNode.removeChild(container)
      }
    })
  }
}

export const toastService = ToastService.getInstance()
