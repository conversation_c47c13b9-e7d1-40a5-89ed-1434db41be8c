<template>
  <div class="h-screen flex flex-col bg-gradient-to-br from-slate-50 to-blue-50 overflow-hidden">
    <!-- 顶部导航栏 -->
    <nav class="glass border-b border-white/30 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <RouterLink to="/" class="text-xl font-bold text-gradient">
            1v1 Chat
          </RouterLink>
        </div>
        
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-700">
            {{ authStore.user?.username }}
            <span v-if="authStore.isAnonymous" class="text-xs text-gray-500">(匿名)</span>
          </span>
          
          <!-- 通知图标 -->
          <NotificationIcon />

          <button
            @click="showCreateLinkModal = true"
            class="btn-secondary text-sm"
          >
            生成链接
          </button>

          <RouterLink
            to="/friends"
            class="btn-secondary"
          >
            好友管理
          </RouterLink>

          <RouterLink
            v-if="authStore.isAdmin"
            to="/admin"
            class="btn-secondary text-sm"
          >
            管理
          </RouterLink>
          
          <!-- 当前用户头像 -->
          <div class="flex items-center space-x-3">
            <UserAvatar
              :username="authStore.user?.username"
              :avatar-url="authStore.user?.avatar_url"
              size="md"
              :editable="true"
            />
            <span class="text-sm text-gray-700">{{ authStore.user?.username }}</span>
          </div>

          <button
            @click="handleLogout"
            class="btn-secondary text-sm"
          >
            登出
          </button>
        </div>
      </div>
    </nav>

    <div class="flex-1 flex overflow-hidden">
      <!-- 侧边栏 - 会话列表 -->
      <div class="w-80 glass border-r border-white/30 flex flex-col">
        <!-- 搜索用户 -->
        <div class="p-4 border-b border-white/30">
          <div class="relative">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索用户..."
              class="input-primary pl-10"
              @input="searchUsers"
            >
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>

        <!-- 搜索结果 -->
        <div v-if="searchResults.length > 0" class="p-4 border-b border-white/30">
          <h3 class="text-sm font-medium text-gray-700 mb-2">搜索结果</h3>
          <div class="space-y-2">
            <button
              v-for="user in searchResults"
              :key="user.id"
              @click="startChatWithUser(user)"
              class="w-full text-left p-3 rounded-lg hover:bg-white/50 transition-colors"
            >
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                  <span class="text-sm font-medium text-primary-600">
                    {{ user.username.charAt(0).toUpperCase() }}
                  </span>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ user.username }}</p>
                  <p class="text-xs text-gray-500">
                    {{ user.is_anonymous ? '匿名用户' : '注册用户' }}
                  </p>
                </div>
              </div>
            </button>
          </div>
        </div>

        <!-- 功能分区标签 -->
        <div class="border-b border-white/30">
          <div class="flex">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="activeTab = tab.id"
              class="flex-1 px-4 py-3 text-sm font-medium transition-colors"
              :class="activeTab === tab.id
                ? 'text-primary-600 border-b-2 border-primary-600 bg-white/30'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/20'"
            >
              {{ tab.name }}
            </button>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="flex-1 overflow-y-auto chat-scrollbar">
          <!-- 最近会话 -->
          <div v-if="activeTab === 'conversations'" class="p-4">
            <div v-if="chatStore.conversations.length === 0" class="text-center py-8">
              <p class="text-gray-500 text-sm">暂无会话</p>
              <p class="text-gray-400 text-xs mt-1">搜索用户开始聊天</p>
            </div>

            <div v-else class="space-y-2">
              <button
                v-for="conversation in chatStore.conversations"
                :key="conversation.user.id"
                @click="startChatWithUser(conversation.user)"
                @contextmenu.prevent="showContextMenu($event, conversation.user)"
                @mousedown="handleMouseDown($event, conversation.user)"
                @mouseup="handleMouseUp"
                class="w-full text-left p-3 rounded-lg hover:bg-white/50 transition-colors"
                :class="{ 'bg-white/70': chatStore.currentChatUser?.id === conversation.user.id }"
              >
                <div class="flex items-center space-x-3">
                  <div class="relative">
                    <UserAvatar
                      :username="conversation.user.username"
                      :avatar-url="conversation.user.avatar_url"
                      size="md"
                    />
                    <div
                      v-if="chatStore.isUserOnline(conversation.user.id)"
                      class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white"
                    ></div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                      <p class="text-sm font-medium text-gray-900 truncate">
                        {{ conversation.user.username }}
                      </p>
                      <div class="flex items-center space-x-2">
                        <p class="text-xs text-gray-500">
                          {{ formatTime(conversation.last_message.timestamp) }}
                        </p>
                        <!-- 未读消息计数圆形图标 -->
                        <div
                          v-if="conversation.unread_count > 0"
                          class="flex items-center justify-center min-w-[20px] h-5 bg-red-500 text-white text-xs font-bold rounded-full px-1.5"
                        >
                          {{ conversation.unread_count > 99 ? '99+' : conversation.unread_count }}
                        </div>
                      </div>
                    </div>
                    <p class="text-xs text-gray-600 truncate">
                      {{ conversation.last_message.is_recalled ? '消息已撤回' : conversation.last_message.content }}
                    </p>
                  </div>
                </div>
              </button>
            </div>
          </div>

          <!-- 好友列表 -->
          <div v-else-if="activeTab === 'friends'" class="p-4">
            <!-- 测试按钮 -->
            <div class="mb-4 flex justify-between items-center">
              <span class="text-sm text-gray-600">好友列表 ({{ friends.length }})</span>
              <button
                @click="testSidebarUpdate"
                class="px-2 py-1 text-xs bg-red-100 text-red-600 rounded hover:bg-red-200"
                title="测试侧边栏更新"
              >
                测试删除
              </button>
            </div>

            <div v-if="loadingFriends" class="text-center py-8">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p class="text-gray-500 text-sm mt-2">加载中...</p>
            </div>

            <div v-else-if="friends.length === 0" class="text-center py-8">
              <p class="text-gray-500 text-sm">暂无好友</p>
              <p class="text-gray-400 text-xs mt-1">去好友管理添加好友</p>
            </div>

            <div v-else class="space-y-2">
              <button
                v-for="friend in friends"
                :key="friend.id"
                @click="startChatWithUser(friend)"
                @contextmenu.prevent="showContextMenu($event, friend)"
                @mousedown="handleMouseDown($event, friend)"
                @mouseup="handleMouseUp"
                class="w-full text-left p-3 rounded-lg hover:bg-white/50 transition-colors"
                :class="{ 'bg-white/70': chatStore.currentChatUser?.id === friend.id }"
              >
                <div class="flex items-center space-x-3">
                  <div class="relative">
                    <UserAvatar
                      :username="friend.username"
                      :avatar-url="friend.avatar_url"
                      size="md"
                    />
                    <div
                      v-if="chatStore.isUserOnline(friend.id)"
                      class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white"
                    ></div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">
                      {{ friend.username }}
                    </p>
                    <p class="text-xs text-gray-600 truncate mt-1">
                      {{ chatStore.isUserOnline(friend.id) ? '在线' : '离线' }}
                    </p>
                  </div>
                </div>
              </button>
            </div>
          </div>

          <!-- 群聊列表 -->
          <div v-else-if="activeTab === 'groups'" class="p-4">
            <div v-if="loadingGroups" class="text-center py-8">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p class="text-gray-500 text-sm mt-2">加载中...</p>
            </div>

            <div v-else-if="groups.length === 0" class="text-center py-8">
              <p class="text-gray-500 text-sm">暂无群聊</p>
              <p class="text-gray-400 text-xs mt-1">去好友管理创建或加入群聊</p>
            </div>

            <div v-else class="space-y-2">
              <button
                v-for="group in groups"
                :key="group.id"
                @click="joinGroupChat(group)"
                class="w-full text-left p-3 rounded-lg hover:bg-white/50 transition-colors"
              >
                <div class="flex items-center space-x-3">
                  <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                    <span class="text-sm font-medium text-primary-600">
                      {{ group.name.charAt(0).toUpperCase() }}
                    </span>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                      <p class="text-sm font-medium text-gray-900 truncate">
                        {{ group.name }}
                      </p>
                      <span class="text-xs text-gray-500">
                        {{ group.member_count }} 人
                      </span>
                    </div>
                    <p class="text-xs text-gray-600 truncate mt-1">
                      {{ group.description || '暂无描述' }}
                    </p>
                  </div>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 主聊天区域 -->
      <div class="flex-1 flex flex-col h-full">
        <div v-if="!chatStore.currentChatUser" class="flex-1 flex items-center justify-center">
          <div class="text-center">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">选择一个用户开始聊天</h3>
            <p class="text-gray-600">搜索用户或从会话列表中选择</p>
          </div>
        </div>

        <div v-else class="flex-1 flex flex-col h-full">
          <!-- 聊天头部信息 -->
          <div class="glass border-b border-white/30 px-6 py-4">
            <div class="flex items-center space-x-3">
              <!-- 对方头像 -->
              <div class="relative">
                <UserAvatar
                  :username="chatStore.currentChatUser.username"
                  :avatar-url="chatStore.currentChatUser.avatar_url"
                  size="md"
                />
                <!-- 在线状态指示器 -->
                <div
                  v-if="chatStore.isUserOnline(chatStore.currentChatUser.id)"
                  class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white"
                ></div>
              </div>

              <!-- 用户信息 -->
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <h3 class="text-lg font-semibold text-gray-900">
                    {{ chatStore.currentChatUser.username }}
                  </h3>
                  <!-- 在线状态文字 -->
                  <span class="text-sm text-gray-500">
                    {{ chatStore.isUserOnline(chatStore.currentChatUser.id) ? '在线' : '离线' }}
                  </span>
                </div>

                <!-- 输入状态 -->
                <div class="text-sm text-gray-400 h-5">
                  <span v-if="chatStore.isUserTyping(chatStore.currentChatUser.id)" class="text-blue-500">
                    正在输入...
                  </span>
                </div>
              </div>

              <!-- Socket.IO连接状态指示器 -->
              <div class="flex items-center space-x-1">
                <div class="w-2 h-2 rounded-full" :class="socketConnected ? 'bg-green-400' : 'bg-red-400'"></div>
                <span class="text-xs text-gray-500">{{ socketConnected ? '已连接' : '未连接' }}</span>
              </div>
            </div>
          </div>

          <!-- 聊天消息区域 -->
          <div
            ref="messagesContainer"
            class="flex-1 overflow-y-auto p-6 space-y-4 pb-4 chat-scrollbar relative"
            @scroll="handleScroll"
          >
            <div
              v-for="message in chatStore.currentMessages"
              :key="message.id"
              class="mb-4"
              :class="{
                'flex justify-center': isSystemMessage(message),
                'flex justify-end': !isSystemMessage(message) && message.sender_id === authStore.user?.id,
                'flex': !isSystemMessage(message) && message.sender_id !== authStore.user?.id
              }"
            >
              <!-- 系统消息显示 -->
              <div v-if="isSystemMessage(message)" class="max-w-md">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                  <div class="flex items-center justify-center mb-2">
                    <svg v-if="message.content.includes('通过邀请码')" class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
                    </svg>
                    <svg v-else class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                    </svg>
                    <span class="text-sm font-medium text-blue-800">
                      {{ message.content.includes('通过邀请码') ? '邀请码使用通知' : '聊天链接使用通知' }}
                    </span>
                  </div>
                  <p class="text-sm text-blue-700">{{ message.content }}</p>
                  <p class="text-xs text-blue-500 mt-2">{{ formatTime(message.timestamp) }}</p>
                </div>
              </div>
              <!-- 对方消息：头像在左侧 -->
              <div v-if="!isSystemMessage(message) && message.sender_id !== authStore.user?.id" class="flex items-start space-x-3 max-w-xs lg:max-w-md">
                <!-- 对方头像 -->
                <UserAvatar
                  :username="message.sender_username"
                  :avatar-url="getMessageSenderAvatar(message)"
                  size="chat"
                  class="flex-shrink-0 mt-1"
                />

                <div class="flex-1">
                  <!-- 发送者名称 -->
                  <div class="text-xs text-gray-500 mb-1 flex items-center h-6">
                    {{ message.sender_username }}
                  </div>

                  <!-- 回复的消息 -->
                  <div v-if="message.parent_message" class="mb-2 p-2 bg-gray-100 rounded-lg text-xs">
                    <p class="text-gray-600">回复 {{ message.parent_message.sender_username }}:</p>
                    <p class="text-gray-800 truncate">{{ message.parent_message.content }}</p>
                  </div>

                  <!-- 消息内容 -->
                  <div
                    class="chat-bubble chat-bubble-received"
                    @contextmenu.prevent="showMessageContextMenu($event, message)"
                    :class="{ 'opacity-70 italic': message.is_recalled }"
                  >
                    <!-- 文本消息 -->
                    <p v-if="message.message_type === 'text' || !message.message_type">{{ getDisplayContent(message) }}</p>

                    <!-- 图片消息 -->
                    <div v-else-if="message.message_type === 'image'" class="space-y-2">
                      <div
                        class="relative cursor-pointer rounded-lg overflow-hidden max-w-xs"
                        @click="!message.isPending && openImageViewer(message.image_url!)"
                      >
                        <img
                          :src="getImageUrl(message.image_thumbnail_url || message.image_url)"
                          :alt="message.content || '图片'"
                          class="w-full h-auto hover:opacity-90 transition-opacity duration-200"
                          :class="{ 'opacity-60': message.isPending }"
                          loading="lazy"
                          @error="handleImageError"
                        />

                        <!-- 上传进度覆盖层 -->
                        <div v-if="message.isPending" class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                          <div class="text-center text-white">
                            <div class="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin mb-2"></div>
                            <div class="text-sm font-medium">{{ message.uploadProgress || 0 }}%</div>
                            <div class="text-xs opacity-80">上传中...</div>
                          </div>
                        </div>
                      </div>
                      <p v-if="message.content" class="text-sm">{{ message.content }}</p>
                    </div>

                    <!-- 问卷消息 -->
                    <div v-else-if="message.message_type === 'questionnaire'" class="space-y-2">
                      <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 max-w-sm shadow-sm hover:shadow-md transition-shadow">
                        <div class="flex items-center space-x-2 mb-3">
                          <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                            </svg>
                          </div>
                          <div class="flex-1">
                            <span class="font-medium text-blue-800 text-sm">问卷调查</span>
                            <div class="text-xs text-blue-600 opacity-75">来自 {{ message.sender_username }}</div>
                          </div>
                        </div>
                        <h4 class="font-medium text-gray-900 mb-2 leading-tight">{{ message.content }}</h4>
                        <p class="text-sm text-gray-600 mb-3">点击下方按钮开始填写问卷</p>
                        <button
                          @click="openQuestionnaire(message)"
                          class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-sm font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                        >
                          <span class="flex items-center justify-center space-x-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            <span>开始填写</span>
                          </span>
                        </button>
                      </div>
                    </div>

                    <!-- 问卷回答消息 -->
                    <div v-else-if="message.message_type === 'questionnaire_response'" class="space-y-2">
                      <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4 max-w-sm shadow-sm hover:shadow-md transition-shadow">
                        <div class="flex items-center space-x-2 mb-3">
                          <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                          </div>
                          <div class="flex-1">
                            <span class="font-medium text-green-800 text-sm">问卷已完成</span>
                            <div class="text-xs text-green-600 opacity-75">来自 {{ message.sender_username }}</div>
                          </div>
                        </div>
                        <p class="text-gray-900 mb-3 leading-tight">{{ message.content }}</p>
                        <button
                          @click="viewQuestionnaireResponse(message)"
                          class="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white text-sm font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                        >
                          <span class="flex items-center justify-center space-x-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 616 0z"></path>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            <span>查看回答</span>
                          </span>
                        </button>
                      </div>
                    </div>

                    <!-- 揭秘申请消息 -->
                    <div v-else-if="message.message_type === 'reveal_request'" class="space-y-2">
                      <div class="bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-4 max-w-sm shadow-sm hover:shadow-md transition-shadow">
                        <div class="flex items-center space-x-2 mb-3">
                          <div class="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                          </div>
                          <div class="flex-1">
                            <span class="font-medium text-orange-800 text-sm">揭秘申请</span>
                            <div class="text-xs text-orange-600 opacity-75">来自 {{ message.sender_username }}</div>
                          </div>
                        </div>
                        <p class="text-gray-900 mb-3 leading-tight">{{ message.content }}</p>
                        <div class="flex space-x-2">
                          <button
                            @click="approveRevealRequest(message)"
                            class="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white text-sm font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                          >
                            同意
                          </button>
                          <button
                            @click="rejectRevealRequest(message)"
                            class="flex-1 bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white text-sm font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                          >
                            拒绝
                          </button>
                        </div>
                      </div>
                    </div>

                    <div class="flex items-center justify-between mt-1">
                      <span class="text-xs opacity-70">
                        {{ formatTime(message.timestamp) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 自己的消息：头像在右侧 -->
              <div v-else-if="!isSystemMessage(message)" class="flex items-start space-x-3 max-w-xs lg:max-w-md">
                <div class="flex-1">
                  <!-- 回复的消息 -->
                  <div v-if="message.parent_message" class="mb-2 p-2 bg-gray-100 rounded-lg text-xs text-right">
                    <p class="text-gray-600">回复 {{ message.parent_message.sender_username }}:</p>
                    <p class="text-gray-800 truncate">{{ message.parent_message.content }}</p>
                  </div>

                  <!-- 消息内容 -->
                  <div
                    class="chat-bubble chat-bubble-sent"
                    @contextmenu.prevent="showMessageContextMenu($event, message)"
                    @mousedown="handleMessageMouseDown($event, message)"
                    @mouseup="handleMessageMouseUp"
                    @mouseleave="handleMessageMouseUp"
                    :class="{ 'opacity-70 italic': message.is_recalled }"
                  >
                    <!-- 文本消息 -->
                    <p v-if="message.message_type === 'text' || !message.message_type">{{ getDisplayContent(message) }}</p>

                    <!-- 图片消息 -->
                    <div v-else-if="message.message_type === 'image'" class="space-y-2">
                      <div
                        class="relative cursor-pointer rounded-lg overflow-hidden max-w-xs"
                        @click="!message.isPending && openImageViewer(message.image_url!)"
                      >
                        <img
                          :src="getImageUrl(message.image_thumbnail_url || message.image_url)"
                          :alt="message.content || '图片'"
                          class="w-full h-auto hover:opacity-90 transition-opacity duration-200"
                          :class="{ 'opacity-60': message.isPending }"
                          loading="lazy"
                          @error="handleImageError"
                        />

                        <!-- 上传进度覆盖层 -->
                        <div v-if="message.isPending" class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                          <div class="text-center text-white">
                            <div class="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin mb-2"></div>
                            <div class="text-sm font-medium">{{ message.uploadProgress || 0 }}%</div>
                            <div class="text-xs opacity-80">上传中...</div>
                          </div>
                        </div>

                        <!-- 发送成功标记 -->
                        <div v-else-if="!message.isPending && message.id && !message.id.toString().startsWith('temp_')"
                             class="absolute top-2 right-2 bg-green-500 text-white rounded-full p-1">
                          <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                          </svg>
                        </div>
                      </div>
                      <p v-if="message.content" class="text-sm">{{ message.content }}</p>
                    </div>

                    <!-- 问卷消息 -->
                    <div v-else-if="message.message_type === 'questionnaire'" class="space-y-2">
                      <div class="bg-gradient-to-l from-primary-50 to-blue-50 border border-primary-200 rounded-lg p-4 max-w-sm shadow-sm">
                        <div class="flex items-center space-x-2 mb-3">
                          <div class="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                            </svg>
                          </div>
                          <div class="flex-1 text-right">
                            <span class="font-medium text-primary-800 text-sm">问卷调查</span>
                            <div class="text-xs text-primary-600 opacity-75">已发送</div>
                          </div>
                        </div>
                        <h4 class="font-medium text-gray-900 mb-2 leading-tight text-right">{{ message.content }}</h4>
                        <div class="flex items-center justify-end space-x-2 text-sm text-gray-500">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                          </svg>
                          <span>问卷已发送</span>
                        </div>
                      </div>
                    </div>

                    <!-- 问卷回答消息 -->
                    <div v-else-if="message.message_type === 'questionnaire_response'" class="space-y-2">
                      <div class="bg-gradient-to-l from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4 max-w-sm shadow-sm">
                        <div class="flex items-center space-x-2 mb-3">
                          <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                          </div>
                          <div class="flex-1 text-right">
                            <span class="font-medium text-green-800 text-sm">问卷已完成</span>
                            <div class="text-xs text-green-600 opacity-75">已提交</div>
                          </div>
                        </div>
                        <p class="text-gray-900 mb-3 leading-tight text-right">{{ message.content }}</p>
                        <button
                          @click="viewQuestionnaireResponse(message)"
                          class="w-full bg-gradient-to-l from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white text-sm font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                        >
                          <span class="flex items-center justify-center space-x-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            <span>查看回答</span>
                          </span>
                        </button>
                      </div>
                    </div>

                    <!-- 揭秘申请消息 -->
                    <div v-else-if="message.message_type === 'reveal_request'" class="space-y-2">
                      <div class="bg-gradient-to-l from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-4 max-w-sm shadow-sm">
                        <div class="flex items-center space-x-2 mb-3">
                          <div class="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 616 0z"></path>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                          </div>
                          <div class="flex-1 text-right">
                            <span class="font-medium text-orange-800 text-sm">揭秘申请</span>
                            <div class="text-xs text-orange-600 opacity-75">已发送</div>
                          </div>
                        </div>
                        <p class="text-gray-900 mb-3 leading-tight text-right">{{ message.content }}</p>
                      </div>
                    </div>

                    <div class="flex items-center justify-between mt-1">
                      <span class="text-xs opacity-70">
                        {{ formatTime(message.timestamp) }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 自己的头像 -->
                <UserAvatar
                  :username="authStore.user?.username"
                  :avatar-url="authStore.user?.avatar_url"
                  size="chat"
                  class="flex-shrink-0 mt-1"
                />
              </div>
            </div>

            <!-- 新消息提示 -->
            <div
              v-if="newMessageCount > 0 && !isAtBottom"
              class="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-10"
            >
              <button
                @click="scrollToBottom"
                class="flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-4 py-3 rounded-full shadow-xl transition-all duration-300 animate-pulse hover:animate-none hover:scale-105 backdrop-blur-sm"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
                <span class="text-sm font-medium">
                  {{ newMessageCount === 1 ? '1 条新消息' : `${newMessageCount} 条新消息` }}
                </span>
                <div class="w-2 h-2 bg-white rounded-full animate-ping"></div>
              </button>
            </div>

          </div>

          <!-- 消息输入区域 - 固定在底部 -->
          <div class="flex-shrink-0 border-t border-white/30 p-4 bg-white/80 backdrop-blur-sm">
            <div class="flex space-x-4">
              <div class="flex-1">
                <textarea
                  v-model="messageContent"
                  @keydown.enter.exact.prevent="sendMessage"
                  @keydown.enter.shift.exact="messageContent += '\n'"
                  @input="handleTyping"
                  placeholder="输入消息... (Enter发送，Shift+Enter换行)"
                  class="input-primary resize-none"
                  rows="2"
                  :disabled="chatStore.loading"
                ></textarea>
              </div>
              <div class="flex space-x-2">
                <!-- 图片上传按钮 -->
                <ImageUploader
                  @upload-start="handleImageUploadStart"
                  @upload-progress="handleImageUploadProgress"
                  @upload-success="handleImageUploadSuccess"
                  @upload-error="handleImageUploadError"
                  :disabled="chatStore.loading"
                />

                <!-- 问卷发送按钮（仅管理员可见） -->
                <button
                  v-if="authStore.isAdmin"
                  @click="showQuestionnaireSelector = true"
                  class="btn-secondary flex items-center space-x-2"
                  :disabled="chatStore.loading"
                >
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                  </svg>
                  <span>问卷</span>
                </button>

                <button
                  @click="sendMessage"
                  :disabled="!messageContent.trim() || chatStore.loading"
                  class="btn-primary px-6"
                >
                  发送
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 问卷选择器模态框 -->
    <div v-if="showQuestionnaireSelector" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="card-glass max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900">选择问卷</h3>
          <button @click="showQuestionnaireSelector = false" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div v-if="loadingQuestionnaires" class="text-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p class="mt-2 text-gray-600">加载问卷列表...</p>
        </div>

        <div v-else-if="availableQuestionnaires.length === 0" class="text-center py-8">
          <p class="text-gray-600">暂无可用问卷</p>
          <p class="text-sm text-gray-500 mt-2">请先在管理后台创建问卷</p>
        </div>

        <div v-else class="space-y-3">
          <div
            v-for="questionnaire in availableQuestionnaires"
            :key="questionnaire.id"
            @click="sendQuestionnaire(questionnaire)"
            class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
          >
            <h4 class="font-medium text-gray-900">{{ questionnaire.title }}</h4>
            <p v-if="questionnaire.description" class="text-sm text-gray-600 mt-1">
              {{ questionnaire.description }}
            </p>
            <div class="flex items-center space-x-4 text-xs text-gray-500 mt-2">
              <span>{{ questionnaire.pages.length }} 页</span>
              <span>{{ getTotalQuestions(questionnaire) }} 题</span>
              <span>创建时间：{{ formatDate(questionnaire.created_at) }}</span>
            </div>
          </div>
        </div>

        <div class="flex justify-end mt-6">
          <button @click="showQuestionnaireSelector = false" class="btn-secondary">
            取消
          </button>
        </div>
      </div>
    </div>

    <!-- 生成链接模态框 -->
    <div v-if="showCreateLinkModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="card-glass max-w-md w-full mx-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">生成聊天链接</h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              有效期 (小时)
            </label>
            <select v-model="linkExpireHours" class="input-primary">
              <option value="1">1小时</option>
              <option value="6">6小时</option>
              <option value="24">24小时</option>
              <option value="168">7天</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              备注（可选）
            </label>
            <input
              v-model="linkNote"
              type="text"
              placeholder="为这个链接添加备注，例如：工作交流、朋友聊天等"
              class="input-primary"
              maxlength="255"
            />
            <p class="text-xs text-gray-500 mt-1">{{ linkNote.length }}/255 字符</p>
          </div>

          <div class="flex items-center">
            <input
              id="singleUse"
              v-model="linkSingleUse"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            >
            <label for="singleUse" class="ml-2 block text-sm text-gray-700">
              单次使用
            </label>
          </div>
        </div>
        
        <div class="flex space-x-3 mt-6">
          <button
            @click="showCreateLinkModal = false"
            class="btn-secondary flex-1"
          >
            取消
          </button>
          <button
            @click="createChatLink"
            :disabled="chatStore.loading"
            class="btn-primary flex-1"
          >
            {{ chatStore.loading ? '生成中...' : '生成' }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 右键菜单 -->
  <ContextMenu
    :visible="contextMenu.visible"
    :position="contextMenu.position"
    :menu-items="contextMenu.items"
    @close="hideContextMenu"
    @action="handleContextMenuAction"
  />

  <!-- 消息右键菜单 -->
  <ContextMenu
    :visible="messageContextMenu.visible"
    :position="messageContextMenu.position"
    :menu-items="messageContextMenu.items"
    @close="hideMessageContextMenu"
    @action="handleMessageContextMenuAction"
  />

  <!-- 图片查看器 -->
  <ImageViewer
    :visible="imageViewer.visible"
    :image-url="imageViewer.imageUrl"
    :alt="imageViewer.alt"
    @close="closeImageViewer"
  />

  <!-- 问卷模态框 -->
  <QuestionnaireModal
    :show="showQuestionnaireModal"
    :questionnaire-id="currentQuestionnaireId"
    :message-id="currentMessageId"
    :sender-id="currentSenderId"
    @close="closeQuestionnaireModal"
    @submitted="handleQuestionnaireSubmitted"
  />

  <!-- 问卷回答详情模态框 -->
  <QuestionnaireResponseModal
    :show="showResponseModal"
    :response-id="currentResponseId"
    @close="closeResponseModal"
  />

  <!-- 通知消息 -->
  <div
    v-if="notification"
    class="fixed top-4 right-4 glass rounded-lg p-4 shadow-lg z-50"
    :class="notification.type === 'success' ? 'border-green-200' : 'border-red-200'"
  >
    <div class="flex items-center">
      <div class="flex-shrink-0">
        <svg
          v-if="notification.type === 'success'"
          class="h-5 w-5 text-green-400"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <svg
          v-else
          class="h-5 w-5 text-red-400"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>
      </div>
      <div class="ml-3">
        <p class="text-sm font-medium" :class="notification.type === 'success' ? 'text-green-800' : 'text-red-800'">
          {{ notification.message }}
        </p>
      </div>
    </div>
  </div>

  <!-- Debug Info (only in development) -->
  <!-- <DebugInfo v-if="isDev" /> -->
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useChatStore } from '@/stores/chat'
import { useNotificationsStore } from '@/stores/notifications'
import { socketService } from '@/services/socket'
import { friendsAPI, groupsAPI } from '@/services/api'
import { questionnaireAPI } from '@/services/questionnaire'
import NotificationIcon from '@/components/NotificationIcon.vue'
import ContextMenu from '@/components/ContextMenu.vue'
import UserAvatar from '@/components/UserAvatar.vue'
// import DebugInfo from '@/components/DebugInfo.vue'
import ImageViewer from '@/components/ImageViewer.vue'
import ImageUploader from '@/components/ImageUploader.vue'
import QuestionnaireModal from '@/components/QuestionnaireModal.vue'
import QuestionnaireResponseModal from '@/components/QuestionnaireResponseModal.vue'
import { formatTime } from '@/utils/time'
import type { User, Group, Questionnaire } from '@/types'
import type { MenuItem } from '@/components/ContextMenu.vue'

// 接收路由参数
interface Props {
  userId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  userId: null
})

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const chatStore = useChatStore()
const notificationsStore = useNotificationsStore()

// 开发环境检查
const isDev = import.meta.env.DEV

const searchQuery = ref('')
const searchResults = ref<User[]>([])
const messageContent = ref('')
const showCreateLinkModal = ref(false)
const linkExpireHours = ref(24)
const linkSingleUse = ref(true)
const linkNote = ref('')
const notification = ref<{ type: 'success' | 'error', message: string } | null>(null)
const socketConnected = ref(false)
let connectionCheckInterval: NodeJS.Timeout | null = null

// 问卷相关
const showQuestionnaireSelector = ref(false)
const availableQuestionnaires = ref<Questionnaire[]>([])
const loadingQuestionnaires = ref(false)

// 问卷模态框相关
const showQuestionnaireModal = ref(false)
const currentQuestionnaireId = ref<number | undefined>()
const currentMessageId = ref<number | undefined>()
const currentSenderId = ref<number | undefined>()

// 问卷回答详情模态框相关
const showResponseModal = ref(false)
const currentResponseId = ref<number | undefined>()

// 功能分区相关
const activeTab = ref('conversations')
const friends = ref<User[]>([])
const groups = ref<Group[]>([])
const loadingFriends = ref(false)
const loadingGroups = ref(false)

const tabs = [
  { id: 'conversations', name: '最近会话' },
  { id: 'friends', name: '好友' },
  { id: 'groups', name: '群聊' }
]

// 右键菜单相关
const contextMenu = ref({
  visible: false,
  position: { x: 0, y: 0 },
  items: [] as MenuItem[],
  targetUser: null as User | null
})

// 消息右键菜单相关
const messageContextMenu = ref({
  visible: false,
  position: { x: 0, y: 0 },
  items: [] as MenuItem[],
  targetMessage: null as any
})

// 长按相关
let longPressTimer: NodeJS.Timeout | null = null
let messageLongPressTimer: NodeJS.Timeout | null = null
const longPressDelay = 500 // 500ms

let typingTimeout: NodeJS.Timeout | null = null

// 新消息提示相关
const messagesContainer = ref<HTMLElement | null>(null)
const newMessageCount = ref(0)
const isAtBottom = ref(true)
const lastMessageCount = ref(0)

onMounted(async () => {
  try {
    // 确保用户已认证
    if (!authStore.isAuthenticated) {
      console.log('User not authenticated, redirecting to home')
      router.push('/')
      return
    }

    // 连接Socket.IO（只在聊天页面连接）
    await connectSocketForChat()

    // 加载会话列表
    await chatStore.getConversations()

    // 加载好友和群组列表
    await Promise.all([
      loadFriends(),
      loadGroups()
    ])

    // 如果有userId参数，自动开始与该用户聊天
    if (props.userId) {
      await startChatWithUserId(props.userId)
    }
  } catch (error) {
    console.error('Error during chat component initialization:', error)
    // Continue with socket setup even if some initialization fails
  }

  // 设置Socket.IO事件监听
  setupSocketEventListeners()

  // 初始化新消息提示状态
  lastMessageCount.value = chatStore.currentMessages.length
  isAtBottom.value = true
  newMessageCount.value = 0
})

// 组件卸载时断开Socket连接
onUnmounted(() => {
  console.log('Chat component unmounting, disconnecting socket...')

  // 清理定时器
  if (connectionCheckInterval) {
    clearInterval(connectionCheckInterval)
    connectionCheckInterval = null
  }

  // 断开Socket连接
  socketService.disconnect()
  socketConnected.value = false
})

// 确保Socket连接用于聊天
const connectSocketForChat = async () => {
  if (socketService.isConnected()) {
    console.log('Socket already connected for chat')
    socketConnected.value = true

    // 即使已连接，也要获取在线用户列表
    setTimeout(() => {
      if (socketService.isConnected()) {
        socketService.getOnlineUsers()
        console.log('Requested online users list from existing connection')
      }
    }, 300)
    return
  }

  try {
    console.log('Connecting Socket.IO for chat...')
    await socketService.connect()
    console.log('Socket.IO connected successfully for chat')
    socketConnected.value = true

    // 连接成功后获取在线用户列表
    setTimeout(() => {
      if (socketService.isConnected()) {
        socketService.getOnlineUsers()
        console.log('Requested online users list after socket connection')
      }
    }, 300)
  } catch (error) {
    console.error('Failed to connect Socket.IO for chat:', error)
    socketConnected.value = false
  }
}

// 设置Socket事件监听器
const setupSocketEventListeners = () => {
  // 添加Socket.IO事件监听
  socketService.on('error', (data) => {
    console.error('Socket.IO error in chat:', data)
  })

  socketService.on('connect', () => {
    console.log('🔌 Socket.IO connected in chat page')
    socketConnected.value = true

    // 重要：Socket重连后，检查全局监听器是否还在
    console.log('🔍 Chat page: Checking if global listeners are still active after reconnection...')

    // 连接成功后获取在线用户列表
    setTimeout(() => {
      socketService.getOnlineUsers()
      console.log('Requested online users list after socket connection')
    }, 100)

    // 如果当前有聊天用户，重新加入对话房间
    if (chatStore.currentChatUser) {
      console.log('Rejoining conversation room for user:', chatStore.currentChatUser.id)
      socketService.joinConversation(chatStore.currentChatUser.id)

      // 重新加载消息以确保显示最新内容
      setTimeout(async () => {
        try {
          console.log('Reloading messages for current chat user')
          await chatStore.getMessages(chatStore.currentChatUser!.id)
        } catch (error) {
          console.warn('Failed to reload messages:', error)
        }
      }, 200)
    }
  })

  socketService.on('disconnect', () => {
    console.log('Socket.IO disconnected in chat')
    socketConnected.value = false
  })

  // 监听消息发送确认
  socketService.on('message_sent', (data: { message: any; status: string }) => {
    console.log('Message sent confirmation:', data)
    // 将真实消息添加到聊天记录
    chatStore.addMessage(data.message)

    // 移除所有临时消息（因为真实消息已经到达）
    // 这里我们简单地清空所有临时消息，因为真实消息已经包含了所有信息
    const tempIds = Array.from(chatStore.pendingMessages.keys())
    tempIds.forEach(tempId => {
      chatStore.removePendingMessage(tempId)
    })
  })

  // 监听新消息
  socketService.on('new_message', (data: { message: any }) => {
    console.log('New message received:', data)
    chatStore.addMessage(data.message)
  })

  // 监听新对话
  socketService.on('new_conversation', (data: { user: any; message: any }) => {
    console.log('New conversation received:', data)
    chatStore.addNewConversation(data.user, data.message)
  })

  // 监听好友被删除事件
  socketService.on('friend_removed', (data: { user_id: number; username: string; removed_by: string }) => {
    console.log('🔥 Friend removed in chat:', data)

    // 从对话列表中移除该用户 (修复：使用 conv.user.id 而不是 conv.id)
    const conversationIndex = chatStore.conversations.findIndex(conv => conv.user.id === data.user_id)
    if (conversationIndex > -1) {
      console.log(`🗑️ Removing conversation with ${data.username} from conversations list`)
      chatStore.conversations.splice(conversationIndex, 1)
    }

    // 从左侧边栏的好友列表中移除该用户
    const friendIndex = friends.value.findIndex(friend => friend.id === data.user_id)
    if (friendIndex > -1) {
      console.log(`🗑️ Removing ${data.username} from friends list`)
      friends.value.splice(friendIndex, 1)
    }

    // 如果当前正在与被删除的好友聊天，清空聊天
    if (chatStore.currentChatUser && chatStore.currentChatUser.id === data.user_id) {
      console.log(`🚫 Clearing current chat with ${data.username}`)
      chatStore.currentChatUser = null
      chatStore.messages = []
    }

    // 显示通知
    if (data.removed_by === 'other') {
      showNotification(`${data.username} 删除了你的好友关系`, 'error')
    } else {
      console.log(`✅ Successfully removed friend ${data.username}`)
    }
  })

  // 监听消息撤回
  socketService.on('message_recalled', (data: { message: any }) => {
    console.log('Message recalled:', data)
    chatStore.updateMessage(data.message)
  })

  // 监听用户输入状态
  socketService.on('user_typing', (data: { user_id: number; username: string; is_typing: boolean }) => {
    chatStore.updateUserTyping(data.user_id, data.is_typing)
  })

  // 监听在线用户列表
  socketService.on('online_users', (data: { users: Array<{ user_id: number; username: string; connected_at: string }> }) => {
    console.log('Online users received:', data.users)
    chatStore.updateOnlineUsers(data.users)
  })

  // 监听用户上线
  socketService.on('user_online', (data: { user_id: number; username: string }) => {
    console.log('User came online:', data)
    chatStore.addOnlineUser(data.user_id, data.username)
  })

  // 监听用户下线
  socketService.on('user_offline', (data: { user_id: number; username: string }) => {
    console.log('User went offline:', data)
    chatStore.removeOnlineUser(data.user_id)
  })

  // 监听好友请求相关事件 - 确保Chat页面也能收到通知
  // 注意：这些事件的主要处理逻辑在App.vue中，这里只是确保事件能被接收
  socketService.on('friend_request_received', (data) => {
    console.log('🔔 Chat page: Friend request received event detected:', data)
    // 主要的通知处理由App.vue的全局监听器负责
    // 这里只需要确保事件不被遗漏，可以添加Chat页面特有的处理逻辑

    // 刷新好友列表以显示新的好友请求
    loadFriends()
  })

  socketService.on('friend_request_accepted', (data) => {
    console.log('✅ Chat page: Friend request accepted:', data)
    // 刷新好友列表
    loadFriends()
  })

  socketService.on('friend_request_declined', (data) => {
    console.log('❌ Chat page: Friend request declined:', data)
    // 刷新好友列表
    loadFriends()
  })

  // 初始化连接状态
  socketConnected.value = socketService.isConnected()

  // 定期检查连接状态
  connectionCheckInterval = setInterval(() => {
    socketConnected.value = socketService.isConnected()
  }, 1000)
}

// 监听路由参数变化
watch(() => props.userId, async (newUserId) => {
  if (newUserId) {
    await startChatWithUserId(newUserId)
  }
})

// 监听消息变化
watch(() => chatStore.currentMessages, () => {
  checkForNewMessages()
}, { deep: true })

// 监听当前聊天用户变化
watch(() => chatStore.currentChatUser, () => {
  // 切换聊天对象时重置状态
  newMessageCount.value = 0
  isAtBottom.value = true
  lastMessageCount.value = chatStore.currentMessages.length

  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
})

// 根据用户ID开始聊天
const startChatWithUserId = async (userId: number) => {
  try {
    // 首先尝试从会话列表中找到用户
    let user = chatStore.conversations.find(conv => conv.user.id === userId)?.user

    if (!user) {
      // 如果会话列表中没有，通过API获取用户信息
      user = await chatStore.getUserById(userId)
    }

    if (user) {
      await startChatWithUser(user)
    } else {
      console.error('User not found:', userId)
    }
  } catch (error) {
    console.error('Failed to start chat with user:', error)
  }
}

onUnmounted(() => {
  if (typingTimeout) {
    clearTimeout(typingTimeout)
  }

  // 清理连接检查定时器
  if (connectionCheckInterval) {
    clearInterval(connectionCheckInterval)
  }

  // 清理长按定时器
  if (longPressTimer) {
    clearTimeout(longPressTimer)
  }

  // 移除Socket.IO事件监听
  socketService.off('connect')
  socketService.off('disconnect')
  socketService.off('error')

  // 注意：不移除好友请求相关的监听器，因为它们应该由App.vue全局管理
})

// 搜索用户
const searchUsers = async () => {
  if (!searchQuery.value.trim()) {
    searchResults.value = []
    return
  }
  
  try {
    const users = await chatStore.searchUsers(searchQuery.value)
    searchResults.value = users.filter(user => user.id !== authStore.user?.id)
  } catch (error) {
    console.error('Failed to search users:', error)
  }
}

// 开始与用户聊天
const startChatWithUser = async (user: User) => {
  console.log('Starting chat with user:', user)
  searchQuery.value = ''
  searchResults.value = []

  // 设置当前聊天用户（这会触发消息加载）
  await chatStore.setCurrentChatUser(user)

  // 加入对话房间以接收实时消息
  if (socketService.isConnected()) {
    try {
      socketService.joinConversation(user.id)

      // 获取在线用户列表以更新状态
      socketService.getOnlineUsers()
      console.log('Joined conversation and requested online users for:', user.username)
      console.log('Joined conversation room for user:', user.id)
    } catch (error) {
      console.warn('Failed to join conversation room:', error)
    }
  } else {
    console.warn('Socket not connected, cannot join conversation room')
  }

  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

// 右键菜单处理
const showContextMenu = (event: MouseEvent, user: User) => {
  contextMenu.value.targetUser = user
  contextMenu.value.position = { x: event.clientX, y: event.clientY }
  contextMenu.value.items = [
    {
      label: '删除对话',
      icon: 'delete',
      action: 'delete-conversation',
      danger: true
    },
    {
      label: '删除联系人',
      icon: 'user-remove',
      action: 'delete-contact',
      danger: true
    }
  ]
  contextMenu.value.visible = true
}

const hideContextMenu = () => {
  contextMenu.value.visible = false
  contextMenu.value.targetUser = null
}

// 消息右键菜单处理
const showMessageContextMenu = (event: MouseEvent, message: any) => {
  // 检查是否可以显示消息菜单
  if (!canShowMessageMenu(message)) {
    return
  }

  messageContextMenu.value.targetMessage = message
  messageContextMenu.value.position = { x: event.clientX, y: event.clientY }
  messageContextMenu.value.items = [
    {
      label: '撤回消息',
      icon: 'recall',
      action: 'recall-message',
      danger: true
    }
  ]
  messageContextMenu.value.visible = true
}

const hideMessageContextMenu = () => {
  messageContextMenu.value.visible = false
  messageContextMenu.value.targetMessage = null
}

// 消息长按处理
const handleMessageMouseDown = (event: MouseEvent, message: any) => {
  // 只处理左键
  if (event.button !== 0) return

  // 检查是否可以显示消息菜单
  if (!canShowMessageMenu(message)) {
    return
  }

  messageLongPressTimer = setTimeout(() => {
    showMessageContextMenu(event, message)
  }, longPressDelay)
}

const handleMessageMouseUp = () => {
  if (messageLongPressTimer) {
    clearTimeout(messageLongPressTimer)
    messageLongPressTimer = null
  }
}

const handleContextMenuAction = async (action: string) => {
  const user = contextMenu.value.targetUser
  if (!user) return

  switch (action) {
    case 'delete-conversation':
      await deleteConversation(user)
      break
    case 'delete-contact':
      await deleteContact(user)
      break
  }
}

const handleMessageContextMenuAction = (action: string) => {
  if (!messageContextMenu.value.targetMessage) return

  switch (action) {
    case 'recall-message':
      const message = messageContextMenu.value.targetMessage

      // 检查是否可以撤回消息
      if (!canRecallMessage(message)) {
        // 如果是时间超限，给出具体提示
        if (!authStore.isAdmin && message.sender_id === authStore.user?.id) {
          const messageTime = new Date(message.timestamp)
          const now = new Date()
          const timeDiff = now.getTime() - messageTime.getTime()
          const oneMinute = 60 * 1000

          if (timeDiff > oneMinute) {
            alert('消息发送超过1分钟，无法撤回')
            hideMessageContextMenu()
            return
          }
        }
        alert('无法撤回此消息')
        hideMessageContextMenu()
        return
      }

      recallMessage(message.id)
      break
  }

  hideMessageContextMenu()
}

// 长按处理
const handleMouseDown = (event: MouseEvent, user: User) => {
  // 只处理左键
  if (event.button !== 0) return

  longPressTimer = setTimeout(() => {
    showContextMenu(event, user)
  }, longPressDelay)
}

const handleMouseUp = () => {
  if (longPressTimer) {
    clearTimeout(longPressTimer)
    longPressTimer = null
  }
}

// 删除对话
const deleteConversation = async (user: User) => {
  if (!confirm(`确定要删除与 ${user.username} 的对话吗？这将删除所有聊天记录。`)) return

  try {
    // 调用store中的删除对话方法
    await chatStore.deleteConversation(user.id)
    console.log(`已删除与 ${user.username} 的对话`)
  } catch (error) {
    console.error('删除对话失败:', error)
    alert('删除对话失败，请重试')
  }
}

// 删除联系人
const deleteContact = async (user: User) => {
  if (!confirm(`确定要删除联系人 ${user.username} 吗？这将同时删除所有聊天记录。`)) return

  try {
    // 调用API删除好友关系
    await friendsAPI.removeFriend(user.id)

    // 删除对话记录
    await chatStore.deleteConversation(user.id)

    console.log(`已删除联系人 ${user.username}`)
  } catch (error) {
    console.error('删除联系人失败:', error)
    alert('删除联系人失败，请重试')
  }
}

// 获取消息发送者头像
const getMessageSenderAvatar = (message: any) => {
  // 如果是当前聊天用户发送的消息，返回当前聊天用户的头像
  if (message.sender_id === chatStore.currentChatUser?.id) {
    return chatStore.currentChatUser?.avatar_url
  }

  // 如果是自己发送的消息，返回自己的头像
  if (message.sender_id === authStore.user?.id) {
    return authStore.user?.avatar_url
  }

  // 尝试从会话列表中找到发送者的头像
  const conversation = chatStore.conversations.find(conv => conv.user.id === message.sender_id)
  if (conversation) {
    return conversation.user.avatar_url
  }

  // 默认返回null，将显示用户名首字母
  return null
}

// 发送消息
const sendMessage = async () => {
  if (!messageContent.value.trim() || !chatStore.currentChatUser) {
    console.log('Cannot send message: missing content or user')
    return
  }

  console.log('Attempting to send message:', {
    content: messageContent.value.trim(),
    recipient: chatStore.currentChatUser.username,
    socketConnected: socketService.isConnected()
  })

  // 检查Socket连接状态
  if (!socketService.isConnected()) {
    console.error('Socket not connected, cannot send message')
    alert('连接已断开，请刷新页面重试')
    return
  }

  try {
    await chatStore.sendMessage(
      messageContent.value.trim(),
      chatStore.currentChatUser.id
    )
    messageContent.value = ''
  } catch (error) {
    console.error('Failed to send message:', error)
    alert('发送消息失败，请重试')
  }
}

// 撤回消息
const recallMessage = async (messageId: number) => {
  try {
    await chatStore.recallMessage(messageId)
  } catch (error) {
    console.error('Failed to recall message:', error)
  }
}

// 处理图片上传开始
const handleImageUploadStart = (data: { tempId: string; file: File; previewUrl: string }) => {
  if (!chatStore.currentChatUser) {
    console.error('No current chat user selected')
    alert('请先选择聊天对象')
    return
  }

  const authStore = useAuthStore()

  // 添加临时消息到聊天记录
  chatStore.addPendingMessage(data.tempId, {
    sender_id: authStore.user?.id,
    recipient_id: chatStore.currentChatUser.id,
    sender_username: authStore.user?.username,
    message_type: 'image',
    image_url: data.previewUrl, // 使用预览URL
    image_thumbnail_url: data.previewUrl,
    content: '',
    uploadProgress: 0,
    isUploading: true
  })

  console.log('Image upload started:', data.tempId)
}

// 处理图片上传进度
const handleImageUploadProgress = (data: { tempId: string; progress: number }) => {
  chatStore.updatePendingMessageProgress(data.tempId, data.progress)
  console.log('Image upload progress:', data.tempId, data.progress + '%')
}

// 处理图片上传成功
const handleImageUploadSuccess = async (data: { tempId: string; image_url: string; thumbnail_url: string }) => {
  if (!chatStore.currentChatUser) {
    console.error('No current chat user selected')
    return
  }

  // 检查Socket连接状态
  if (!socketService.isConnected()) {
    console.error('Socket not connected, cannot send image')
    alert('连接已断开，请刷新页面重试')
    // 移除临时消息
    chatStore.removePendingMessage(data.tempId)
    return
  }

  try {
    // 更新临时消息为上传完成状态
    const pendingMsg = chatStore.pendingMessages.get(data.tempId)
    if (pendingMsg) {
      pendingMsg.uploadProgress = 100
      pendingMsg.isUploading = false
      pendingMsg.image_url = data.image_url
      pendingMsg.image_thumbnail_url = data.thumbnail_url
      // 触发响应式更新
      chatStore.pendingMessages = new Map(chatStore.pendingMessages)
    }

    // 通过Socket.IO发送图片消息
    socketService.sendMessage({
      content: '', // 图片消息可以没有文字内容
      recipient_id: chatStore.currentChatUser.id,
      message_type: 'image',
      image_url: data.image_url,
      image_thumbnail_url: data.thumbnail_url
    })

    console.log('Image message sent successfully:', data)
  } catch (error) {
    console.error('Failed to send image message:', error)
    alert('发送图片失败，请重试')
    // 移除临时消息
    chatStore.removePendingMessage(data.tempId)
  }
}

// 处理图片上传失败
const handleImageUploadError = (data: { tempId: string; error: string }) => {
  console.error('Image upload error:', data.error)
  alert(`图片上传失败：${data.error}`)
  // 移除临时消息
  chatStore.removePendingMessage(data.tempId)
}

// 加载可用问卷列表
const loadAvailableQuestionnaires = async () => {
  if (!authStore.isAdmin) return

  try {
    loadingQuestionnaires.value = true
    const response = await questionnaireAPI.getQuestionnaireList()
    availableQuestionnaires.value = response.questionnaires.filter((q: Questionnaire) => q.is_active)
  } catch (error) {
    console.error('Failed to load questionnaires:', error)
    alert('加载问卷列表失败')
  } finally {
    loadingQuestionnaires.value = false
  }
}

// 发送问卷
const sendQuestionnaire = async (questionnaire: Questionnaire) => {
  if (!chatStore.currentChatUser) {
    alert('请先选择聊天对象')
    return
  }

  // 检查Socket连接状态
  if (!socketService.isConnected()) {
    alert('连接已断开，请刷新页面重试')
    return
  }

  try {
    // 通过Socket.IO发送问卷消息
    socketService.sendMessage({
      content: `问卷：${questionnaire.title}`,
      recipient_id: chatStore.currentChatUser.id,
      message_type: 'questionnaire',
      questionnaire_id: questionnaire.id
    })

    showQuestionnaireSelector.value = false
    console.log('Questionnaire message sent successfully:', questionnaire.id)
  } catch (error) {
    console.error('Failed to send questionnaire message:', error)
    alert('发送问卷失败，请重试')
  }
}

// 获取问卷总题数
const getTotalQuestions = (questionnaire: Questionnaire) => {
  return questionnaire.pages.reduce((total, page) => total + page.questions.length, 0)
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 打开问卷回答界面
const openQuestionnaire = (message: any) => {
  if (!message.questionnaire_id) {
    alert('问卷信息错误')
    return
  }

  // 使用模态框打开问卷
  currentQuestionnaireId.value = message.questionnaire_id
  currentMessageId.value = message.id
  currentSenderId.value = message.sender_id
  showQuestionnaireModal.value = true
}

// 关闭问卷模态框
const closeQuestionnaireModal = () => {
  showQuestionnaireModal.value = false
  currentQuestionnaireId.value = undefined
  currentMessageId.value = undefined
  currentSenderId.value = undefined
}

// 处理问卷提交完成
const handleQuestionnaireSubmitted = () => {
  // 问卷提交成功后，可以在这里做一些额外的处理
  // 比如刷新消息列表等
  console.log('Questionnaire submitted successfully')
}

// 同意揭秘申请
const approveRevealRequest = async (message: any) => {
  if (!message.reveal_request_id) {
    alert('揭秘申请信息错误')
    return
  }

  try {
    await questionnaireAPI.approveRevealRequest(message.reveal_request_id)
    alert('已同意揭秘申请')
    // 可以在这里更新消息状态或刷新消息列表
  } catch (err: any) {
    alert(err.response?.data?.error || '操作失败')
  }
}

// 拒绝揭秘申请
const rejectRevealRequest = async (message: any) => {
  if (!message.reveal_request_id) {
    alert('揭秘申请信息错误')
    return
  }

  try {
    await questionnaireAPI.rejectRevealRequest(message.reveal_request_id)
    alert('已拒绝揭秘申请')
    // 可以在这里更新消息状态或刷新消息列表
  } catch (err: any) {
    alert(err.response?.data?.error || '操作失败')
  }
}

// 关闭问卷回答详情模态框
const closeResponseModal = () => {
  showResponseModal.value = false
  currentResponseId.value = undefined
}

// 查看问卷回答详情
const viewQuestionnaireResponse = (message: any) => {
  if (!message.questionnaire_response_id) {
    alert('问卷回答信息错误')
    return
  }

  // 使用模态框显示问卷回答详情
  currentResponseId.value = message.questionnaire_response_id
  showResponseModal.value = true
}

// 监听问卷选择器显示状态，加载问卷列表
watch(showQuestionnaireSelector, (newVal) => {
  if (newVal && authStore.isAdmin) {
    loadAvailableQuestionnaires()
  }
})

// 处理输入状态
const handleTyping = () => {
  if (!chatStore.currentChatUser) return
  
  // 发送正在输入状态
  chatStore.sendTyping(chatStore.currentChatUser.id, true)
  
  // 清除之前的超时
  if (typingTimeout) {
    clearTimeout(typingTimeout)
  }
  
  // 3秒后停止输入状态
  typingTimeout = setTimeout(() => {
    if (chatStore.currentChatUser) {
      chatStore.sendTyping(chatStore.currentChatUser.id, false)
    }
  }, 3000)
}

// 创建聊天链接
const createChatLink = async () => {
  try {
    const result = await chatStore.createChatLink(
      linkExpireHours.value,
      linkSingleUse.value,
      linkNote.value.trim() || undefined
    )
    const linkUrl = `${window.location.origin}/chat-link/${result.link.code}`

    // 复制到剪贴板
    await navigator.clipboard.writeText(linkUrl)

    showCreateLinkModal.value = false

    // 清空备注输入框
    linkNote.value = ''

    // 可以显示成功提示
    const noteText = result.link.note ? `（备注：${result.link.note}）` : ''
    alert(`链接已生成并复制到剪贴板！${noteText}`)
  } catch (error) {
    console.error('Failed to create chat link:', error)
  }
}

// 登出
const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push('/')
  } catch (error) {
    console.error('Failed to logout:', error)
  }
}

// 新消息提示相关函数
const handleScroll = () => {
  if (!messagesContainer.value) return

  const container = messagesContainer.value
  const scrollTop = container.scrollTop
  const scrollHeight = container.scrollHeight
  const clientHeight = container.clientHeight

  // 检查是否在底部（允许10px的误差）
  const atBottom = scrollTop + clientHeight >= scrollHeight - 10
  isAtBottom.value = atBottom

  // 如果滚动到底部，清除新消息计数
  if (atBottom) {
    newMessageCount.value = 0
  }
}

const scrollToBottom = () => {
  if (!messagesContainer.value) return

  messagesContainer.value.scrollTo({
    top: messagesContainer.value.scrollHeight,
    behavior: 'smooth'
  })

  // 清除新消息计数
  newMessageCount.value = 0
  isAtBottom.value = true
}

// 检查新消息
const checkForNewMessages = () => {
  const currentMessageCount = chatStore.currentMessages.length

  if (currentMessageCount > lastMessageCount.value) {
    const newMessages = currentMessageCount - lastMessageCount.value

    // 如果用户不在底部，增加新消息计数
    if (!isAtBottom.value) {
      newMessageCount.value += newMessages
    } else {
      // 如果在底部，自动滚动到最新消息
      nextTick(() => {
        scrollToBottom()
      })
    }
  }

  lastMessageCount.value = currentMessageCount
}

// 加载好友列表
const loadFriends = async () => {
  loadingFriends.value = true
  try {
    const response = await friendsAPI.getFriends({})
    friends.value = response.data.friends
  } catch (error) {
    console.error('Failed to load friends:', error)
  } finally {
    loadingFriends.value = false
  }
}

// 加载群组列表
const loadGroups = async () => {
  loadingGroups.value = true
  try {
    const response = await groupsAPI.getUserGroups({})
    groups.value = response.data.groups
  } catch (error) {
    console.error('Failed to load groups:', error)
  } finally {
    loadingGroups.value = false
  }
}

// 测试侧边栏更新功能
const testSidebarUpdate = () => {
  if (friends.value.length > 0 && chatStore.conversations.length > 0) {
    const testFriend = friends.value[0]
    const testConversation = chatStore.conversations[0]

    console.log('🧪 Testing sidebar update...')
    console.log('Current friends:', friends.value.map(f => ({ id: f.id, username: f.username })))
    console.log('Current conversations:', chatStore.conversations.map(c => ({ id: c.user.id, username: c.user.username })))

    // 模拟接收到 friend_removed 事件
    const testData = {
      user_id: testFriend.id,
      username: testFriend.username,
      removed_by: 'test'
    }

    console.log('🔥 Simulating friend_removed event:', testData)

    // 从对话列表中移除该用户
    const conversationIndex = chatStore.conversations.findIndex(conv => conv.user.id === testData.user_id)
    if (conversationIndex > -1) {
      console.log(`🗑️ Removing conversation with ${testData.username} from conversations list`)
      chatStore.conversations.splice(conversationIndex, 1)
    }

    // 从左侧边栏的好友列表中移除该用户
    const friendIndex = friends.value.findIndex(friend => friend.id === testData.user_id)
    if (friendIndex > -1) {
      console.log(`🗑️ Removing ${testData.username} from friends list`)
      friends.value.splice(friendIndex, 1)
    }

    console.log('✅ Test completed!')
    console.log('Updated friends:', friends.value.map(f => ({ id: f.id, username: f.username })))
    console.log('Updated conversations:', chatStore.conversations.map(c => ({ id: c.user.id, username: c.user.username })))
  } else {
    console.log('❌ No friends or conversations to test with')
  }
}

// 跳转到群聊
const joinGroupChat = (group: Group) => {
  router.push(`/group-chat/${group.id}`)
}

// 检查是否可以显示消息菜单
const canShowMessageMenu = (message: any) => {
  // 消息已撤回则不显示菜单
  if (message.is_recalled) {
    return false
  }

  // 管理员可以对任何消息显示菜单
  if (authStore.isAdmin) {
    return true
  }

  // 普通用户只能对自己的消息显示菜单
  return message.sender_id === authStore.user?.id
}

// 检查是否可以撤回消息（实际撤回时的检查）
const canRecallMessage = (message: any) => {
  // 消息已撤回则不能再撤回
  if (message.is_recalled) {
    return false
  }

  // 管理员可以撤回任何消息
  if (authStore.isAdmin) {
    return true
  }

  // 普通用户只能撤回自己的消息
  if (message.sender_id !== authStore.user?.id) {
    return false
  }

  // 检查时间限制：1分钟内
  const messageTime = new Date(message.timestamp)
  const now = new Date()
  const timeDiff = now.getTime() - messageTime.getTime()
  const oneMinute = 60 * 1000 // 1分钟的毫秒数

  return timeDiff <= oneMinute
}

// 判断是否为系统消息
const isSystemMessage = (message: any) => {
  return message.content &&
    ((message.content.includes('通过邀请码') && message.content.includes('加入了对话')) ||
     (message.content.includes('通过聊天链接') && message.content.includes('加入了对话')))
}

// 获取消息显示内容
const getDisplayContent = (message: any) => {
  if (!message.is_recalled) {
    return message.content
  }

  // 如果是管理员且消息被撤回，显示原始内容
  if (authStore.isAdmin && message.original_content) {
    return `撤回了一条消息(${message.original_content})`
  }

  // 普通用户看到的撤回消息
  return message.content
}

// 图片URL处理
const getImageUrl = (url: string | undefined) => {
  if (!url) return ''

  // 如果是相对路径，添加后端服务器地址
  if (url.startsWith('/')) {
    return `http://localhost:5000${url}`
  }

  return url
}

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  console.error('Image failed to load:', img.src)

  // 可以设置一个默认的错误图片
  // img.src = '/path/to/error-image.png'
}

// 图片查看器相关
const imageViewer = ref({
  visible: false,
  imageUrl: '',
  alt: ''
})

// 打开图片查看器
const openImageViewer = (imageUrl: string, alt: string = '图片') => {
  imageViewer.value = {
    visible: true,
    imageUrl: getImageUrl(imageUrl),
    alt
  }
}

// 关闭图片查看器
const closeImageViewer = () => {
  imageViewer.value = {
    visible: false,
    imageUrl: '',
    alt: ''
  }
}

// 通知显示函数
const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
  notification.value = { type, message }
  setTimeout(() => {
    notification.value = null
  }, 3000)
}
</script>

<style scoped>
/* 美化聊天区域滚动条 */
.chat-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.chat-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.3);
  border-radius: 10px;
  margin: 8px 0;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.6), rgba(107, 114, 128, 0.6));
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: all 0.3s ease;
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.8), rgba(75, 85, 99, 0.8));
  border: 1px solid transparent;
}

.chat-scrollbar::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(75, 85, 99, 0.9), rgba(55, 65, 81, 0.9));
}

/* 滚动条角落 */
.chat-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}
</style>
