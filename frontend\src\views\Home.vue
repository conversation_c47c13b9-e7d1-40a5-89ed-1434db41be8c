<template>
  <div class="min-h-screen flex flex-col">
    <!-- 导航栏 -->
    <nav class="glass border-b border-white/30 px-6 py-4">
      <div class="max-w-7xl mx-auto flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <h1 class="text-2xl font-bold text-gradient">1v1 Chat</h1>
          <span class="text-sm text-gray-600">实时聊天平台</span>
        </div>
        
        <div class="flex items-center space-x-4">
          <template v-if="authStore.isAuthenticated">
            <span class="text-sm text-gray-700">
              欢迎, {{ authStore.user?.username }}
              <span v-if="authStore.isAnonymous" class="text-xs text-gray-500">(匿名)</span>
            </span>
            
            <RouterLink 
              to="/chat" 
              class="btn-primary"
            >
              进入聊天
            </RouterLink>
            
            <RouterLink 
              v-if="authStore.isAdmin" 
              to="/admin" 
              class="btn-secondary"
            >
              管理后台
            </RouterLink>
            
            <button 
              @click="handleLogout" 
              class="btn-secondary"
              :disabled="authStore.loading"
            >
              登出
            </button>
          </template>
          
          <template v-else>
            <RouterLink to="/login" class="btn-secondary">登录</RouterLink>
            <RouterLink to="/register" class="btn-primary">注册</RouterLink>
          </template>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <main class="flex-1 flex items-center justify-center px-6 py-12">
      <div class="max-w-4xl mx-auto text-center">
        <!-- 标题区域 -->
        <div class="mb-12">
          <h2 class="text-5xl font-bold text-gray-900 mb-6">
            安全、快速的
            <span class="text-gradient">一对一聊天</span>
          </h2>
          <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            支持匿名聊天和注册用户，实时消息传递，消息撤回，链接分享等功能
          </p>
        </div>

        <!-- 功能卡片 -->
        <div class="grid md:grid-cols-3 gap-8 mb-12">
          <div class="card-glass text-center p-8 animate-fade-in">
            <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">实时聊天</h3>
            <p class="text-gray-600">基于WebSocket的实时消息传递，支持消息撤回和回复功能</p>
          </div>

          <div class="card-glass text-center p-8 animate-fade-in" style="animation-delay: 0.1s">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">匿名聊天</h3>
            <p class="text-gray-600">无需注册即可开始聊天，自动生成匿名身份，保护隐私</p>
          </div>

          <div class="card-glass text-center p-8 animate-fade-in" style="animation-delay: 0.2s">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">链接分享</h3>
            <p class="text-gray-600">生成专属聊天链接，分享给朋友即可开始对话</p>
          </div>
        </div>

        <!-- 演示链接 -->
        <div class="mb-8 space-x-4">
          <RouterLink
            to="/icon-test"
            class="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
            </svg>
            🔔 通知功能测试
          </RouterLink>

          <RouterLink
            to="/socket-debug"
            class="inline-flex items-center px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 717.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"></path>
            </svg>
            🔧 Socket调试
          </RouterLink>

          <RouterLink
            to="/chat-notification-test"
            class="inline-flex items-center px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            💬 Chat通知测试
          </RouterLink>
        </div>

        <!-- 行动按钮 -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <template v-if="authStore.isAuthenticated">
            <RouterLink 
              to="/chat" 
              class="btn-primary text-lg px-8 py-3 animate-pulse-glow"
            >
              开始聊天
            </RouterLink>
            <button 
              @click="createChatLink" 
              class="btn-secondary text-lg px-8 py-3"
              :disabled="chatStore.loading"
            >
              {{ chatStore.loading ? '生成中...' : '生成聊天链接' }}
            </button>
          </template>
          
          <template v-else>
            <button 
              @click="startAnonymousChat" 
              class="btn-primary text-lg px-8 py-3 animate-pulse-glow"
              :disabled="authStore.loading"
            >
              {{ authStore.loading ? '初始化中...' : '匿名开始聊天' }}
            </button>
            <RouterLink 
              to="/register" 
              class="btn-secondary text-lg px-8 py-3"
            >
              注册账号
            </RouterLink>
          </template>
        </div>

        <!-- 聊天链接显示 -->
        <div v-if="generatedLink" class="mt-8 p-6 card-glass max-w-md mx-auto">
          <h4 class="text-lg font-semibold text-gray-900 mb-3">聊天链接已生成</h4>
          <div class="flex items-center space-x-2 mb-3">
            <input 
              :value="generatedLinkUrl" 
              readonly 
              class="input-primary text-sm"
            >
            <button 
              @click="copyLink" 
              class="btn-secondary px-3 py-2 text-sm"
            >
              复制
            </button>
          </div>
          <p class="text-sm text-gray-600">
            链接有效期：{{ generatedLink.expires_at ? new Date(generatedLink.expires_at).toLocaleString() : '未知' }}
          </p>
        </div>
      </div>
    </main>

    <!-- 页脚 -->
    <footer class="glass border-t border-white/30 px-6 py-4">
      <div class="max-w-7xl mx-auto text-center text-sm text-gray-600">
        <p>&copy; 2024 1v1 Chat Platform. 基于 Flask + Vue 3 构建.</p>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useChatStore } from '@/stores/chat'
import type { ChatLink } from '@/types'

const router = useRouter()
const authStore = useAuthStore()
const chatStore = useChatStore()

const generatedLink = ref<ChatLink | null>(null)
const generatedLinkUrl = ref('')

// 开始匿名聊天
const startAnonymousChat = async () => {
  try {
    await authStore.createAnonymousUser()
    router.push('/chat')
  } catch (error) {
    console.error('Failed to start anonymous chat:', error)
  }
}

// 登出
const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push('/')
  } catch (error) {
    console.error('Failed to logout:', error)
  }
}

// 创建聊天链接
const createChatLink = async () => {
  try {
    const result = await chatStore.createChatLink(24, true)
    generatedLink.value = result.link
    generatedLinkUrl.value = `${window.location.origin}${result.url}`
  } catch (error) {
    console.error('Failed to create chat link:', error)
  }
}

// 复制链接
const copyLink = async () => {
  try {
    await navigator.clipboard.writeText(generatedLinkUrl.value)
    // 可以添加一个提示消息
  } catch (error) {
    console.error('Failed to copy link:', error)
  }
}
</script>
