<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- 顶部导航栏 -->
    <nav class="glass border-b border-white/30 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <RouterLink to="/" class="text-xl font-bold text-gradient">
            1v1 Chat
          </RouterLink>
          <span class="text-sm text-gray-600">通知功能演示</span>
        </div>
        
        <div class="flex items-center space-x-4">
          <!-- 通知图标 -->
          <NotificationIcon />
          
          <RouterLink to="/" class="btn-secondary text-sm">
            返回首页
          </RouterLink>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-4xl mx-auto p-6">
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">通知功能演示</h1>
        
        <!-- 功能说明 -->
        <div class="mb-8 p-4 bg-blue-50 rounded-lg">
          <h2 class="text-lg font-semibold text-blue-900 mb-2">已实现的通知功能：</h2>
          <ul class="list-disc list-inside text-blue-800 space-y-1">
            <li>🔔 通知图标红点提示（右上角铃铛图标）</li>
            <li>✨ 新通知动画效果（跳动和脉冲动画）</li>
            <li>🖥️ 浏览器原生桌面通知</li>
            <li>📱 页面内Toast弹窗通知</li>
            <li>🔊 音效提示</li>
            <li>📄 页面标题未读数量提示</li>
          </ul>
        </div>

        <!-- 测试按钮区域 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
          <!-- 模拟好友请求 -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-3">模拟好友请求</h3>
            <button 
              @click="simulateFriendRequest"
              class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              收到好友请求
            </button>
            <p class="text-xs text-gray-500 mt-2">
              触发所有通知效果
            </p>
          </div>

          <!-- 浏览器通知测试 -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-3">浏览器通知</h3>
            <button 
              @click="testBrowserNotification"
              class="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            >
              测试桌面通知
            </button>
            <p class="text-xs text-gray-500 mt-2">
              权限状态: {{ notificationPermission }}
            </p>
          </div>

          <!-- Toast通知测试 -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-3">Toast通知</h3>
            <button 
              @click="testToastNotification"
              class="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
            >
              显示Toast
            </button>
            <p class="text-xs text-gray-500 mt-2">
              页面内弹窗通知
            </p>
          </div>

          <!-- 音效测试 -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-3">音效通知</h3>
            <button 
              @click="testAudioNotification"
              class="w-full px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors"
            >
              播放提示音
            </button>
            <p class="text-xs text-gray-500 mt-2">
              音效状态: {{ audioService.isEnabled() ? '启用' : '禁用' }}
            </p>
          </div>

          <!-- 清空通知 -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-3">清空通知</h3>
            <button 
              @click="clearAllNotifications"
              class="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
            >
              清空所有通知
            </button>
            <p class="text-xs text-gray-500 mt-2">
              重置通知状态
            </p>
          </div>

          <!-- 权限管理 -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-3">权限管理</h3>
            <button 
              @click="requestNotificationPermission"
              class="w-full px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 transition-colors"
            >
              请求通知权限
            </button>
            <p class="text-xs text-gray-500 mt-2">
              获取桌面通知权限
            </p>
          </div>
        </div>

        <!-- 状态信息 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h3 class="font-semibold text-gray-900 mb-3">当前状态</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span class="text-gray-600">好友请求:</span>
              <span class="font-medium ml-1">{{ notificationsStore.friendRequests.length }}</span>
            </div>
            <div>
              <span class="text-gray-600">未读通知:</span>
              <span class="font-medium ml-1">{{ notificationsStore.unreadCount }}</span>
            </div>
            <div>
              <span class="text-gray-600">总未读:</span>
              <span class="font-medium ml-1">{{ notificationsStore.totalUnreadCount }}</span>
            </div>
            <div>
              <span class="text-gray-600">页面标题:</span>
              <span class="font-medium ml-1">{{ document.title }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useNotificationsStore } from '@/stores/notifications'
import { notificationService } from '@/services/notificationService'
import { toastService } from '@/services/toastService'
import { audioService } from '@/services/audioService'
import NotificationIcon from '@/components/NotificationIcon.vue'
import type { FriendRequest } from '@/types'

const notificationsStore = useNotificationsStore()
const notificationPermission = ref<NotificationPermission>('default')

onMounted(() => {
  notificationPermission.value = notificationService.getPermission()
})

// 模拟收到好友请求
const simulateFriendRequest = () => {
  const testRequest: FriendRequest = {
    id: Date.now(),
    sender_id: 999,
    sender_username: `测试用户${Math.floor(Math.random() * 100)}`,
    sender_display_name: `测试用户${Math.floor(Math.random() * 100)}`,
    receiver_id: 1,
    receiver_username: '当前用户',
    receiver_display_name: '当前用户',
    status: 'pending',
    message: '这是一个测试好友请求',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  
  // 添加到store（触发图标红点和动画）
  notificationsStore.addFriendRequest(testRequest)
  
  // 显示浏览器通知
  notificationService.showFriendRequestNotification(testRequest.sender_username)
  
  // 显示Toast通知
  toastService.showFriendRequestToast(testRequest.sender_username)
  
  // 播放音效
  audioService.playNotificationSound()
}

// 测试浏览器通知
const testBrowserNotification = async () => {
  await notificationService.showFriendRequestNotification('测试用户')
}

// 测试Toast通知
const testToastNotification = () => {
  toastService.showFriendRequestToast('测试用户Toast')
}

// 测试音效通知
const testAudioNotification = async () => {
  await audioService.playNotificationSound()
}

// 清空所有通知
const clearAllNotifications = () => {
  notificationsStore.clearAllNotifications()
  notificationsStore.clearFriendRequests()
}

// 请求通知权限
const requestNotificationPermission = async () => {
  notificationPermission.value = await notificationService.requestPermission()
}
</script>

<style scoped>
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.btn-secondary {
  @apply px-4 py-2 bg-white/80 text-gray-700 rounded-lg hover:bg-white/90 transition-colors border border-gray-200;
}
</style>
