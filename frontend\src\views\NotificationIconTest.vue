<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">通知图标测试</h1>
    
    <div class="bg-white p-4 rounded shadow mb-4">
      <h2 class="text-lg font-semibold mb-2">通知图标</h2>
      <NotificationIcon />
    </div>
    
    <div class="bg-white p-4 rounded shadow mb-4">
      <h2 class="text-lg font-semibold mb-2">测试按钮</h2>
      <button 
        @click="addTestRequest"
        class="px-4 py-2 bg-blue-500 text-white rounded mr-2"
      >
        添加好友请求
      </button>
      <button 
        @click="clearAll"
        class="px-4 py-2 bg-red-500 text-white rounded"
      >
        清空通知
      </button>
    </div>
    
    <div class="bg-white p-4 rounded shadow">
      <h2 class="text-lg font-semibold mb-2">状态</h2>
      <p>好友请求: {{ friendRequestCount }}</p>
      <p>总未读: {{ totalUnread }}</p>
    </div>
    
    <div class="mt-4">
      <RouterLink to="/" class="text-blue-500 underline">返回首页</RouterLink>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useNotificationsStore } from '@/stores/notifications'
import NotificationIcon from '@/components/NotificationIcon.vue'

const notificationsStore = useNotificationsStore()

const friendRequestCount = computed(() => notificationsStore.friendRequests.length)
const totalUnread = computed(() => notificationsStore.totalUnreadCount)

const addTestRequest = () => {
  const testRequest = {
    id: Date.now(),
    sender_id: 999,
    sender_username: `测试用户${Math.floor(Math.random() * 100)}`,
    sender_display_name: `测试用户${Math.floor(Math.random() * 100)}`,
    receiver_id: 1,
    receiver_username: '当前用户',
    receiver_display_name: '当前用户',
    status: 'pending' as const,
    message: '这是一个测试好友请求',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  
  notificationsStore.addFriendRequest(testRequest)
}

const clearAll = () => {
  notificationsStore.clearAllNotifications()
  notificationsStore.clearFriendRequests()
}
</script>
