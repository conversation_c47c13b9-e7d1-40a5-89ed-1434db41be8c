<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-8">
    <div class="max-w-2xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">通知功能测试</h1>
      
      <!-- 通知图标测试 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">通知图标</h2>
        <div class="flex items-center space-x-4">
          <NotificationIcon />
          <div class="text-sm text-gray-600">
            <p>未读通知: {{ notificationsStore.totalUnreadCount }}</p>
            <p>好友请求: {{ notificationsStore.friendRequests.length }}</p>
          </div>
        </div>
      </div>

      <!-- 测试按钮 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">测试功能</h2>
        <div class="space-y-4">
          <button 
            @click="addTestFriendRequest"
            class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            添加测试好友请求
          </button>
          
          <button 
            @click="testBrowserNotification"
            class="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            测试浏览器通知
          </button>
          
          <button 
            @click="clearNotifications"
            class="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            清空通知
          </button>
        </div>
      </div>

      <!-- 状态显示 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">当前状态</h2>
        <div class="space-y-2 text-sm">
          <p>好友请求数量: {{ notificationsStore.friendRequests.length }}</p>
          <p>未读通知数量: {{ notificationsStore.unreadCount }}</p>
          <p>总未读数量: {{ notificationsStore.totalUnreadCount }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useNotificationsStore } from '@/stores/notifications'
import { notificationService } from '@/services/notificationService'
import NotificationIcon from '@/components/NotificationIcon.vue'

const notificationsStore = useNotificationsStore()

const addTestFriendRequest = () => {
  const testRequest = {
    id: Date.now(),
    sender_id: 999,
    sender_username: `测试用户${Math.floor(Math.random() * 100)}`,
    sender_display_name: `测试用户${Math.floor(Math.random() * 100)}`,
    receiver_id: 1,
    receiver_username: '当前用户',
    receiver_display_name: '当前用户',
    status: 'pending' as const,
    message: '这是一个测试好友请求',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  
  notificationsStore.addFriendRequest(testRequest)
}

const testBrowserNotification = async () => {
  try {
    await notificationService.showFriendRequestNotification('测试用户')
  } catch (error) {
    console.error('通知失败:', error)
  }
}

const clearNotifications = () => {
  notificationsStore.clearAllNotifications()
  notificationsStore.clearFriendRequests()
}
</script>
