<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">Socket调试页面</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Socket状态 -->
      <div class="bg-white p-4 rounded shadow">
        <h2 class="text-lg font-semibold mb-3">Socket状态</h2>
        <div class="space-y-2 text-sm">
          <p>连接状态: <span :class="socketConnected ? 'text-green-600' : 'text-red-600'">
            {{ socketConnected ? '已连接' : '未连接' }}
          </span></p>
          <p>Socket ID: {{ socketId || '无' }}</p>
          <p>用户认证: <span :class="authStore.isAuthenticated ? 'text-green-600' : 'text-red-600'">
            {{ authStore.isAuthenticated ? '已认证' : '未认证' }}
          </span></p>
          <p>用户名: {{ authStore.user?.username || '无' }}</p>
        </div>
        
        <div class="mt-4 space-x-2">
          <button 
            @click="connectSocket"
            class="px-3 py-1 bg-blue-500 text-white rounded text-sm"
            :disabled="socketConnected"
          >
            连接Socket
          </button>
          <button 
            @click="disconnectSocket"
            class="px-3 py-1 bg-red-500 text-white rounded text-sm"
            :disabled="!socketConnected"
          >
            断开Socket
          </button>
        </div>
      </div>

      <!-- 事件监听 -->
      <div class="bg-white p-4 rounded shadow">
        <h2 class="text-lg font-semibold mb-3">事件监听</h2>
        <div class="space-y-2">
          <button 
            @click="setupTestListeners"
            class="w-full px-3 py-2 bg-green-500 text-white rounded text-sm"
          >
            设置测试监听器
          </button>
          <button 
            @click="sendTestEvent"
            class="w-full px-3 py-2 bg-purple-500 text-white rounded text-sm"
          >
            发送测试事件
          </button>
          <button 
            @click="clearLogs"
            class="w-full px-3 py-2 bg-gray-500 text-white rounded text-sm"
          >
            清空日志
          </button>
        </div>
      </div>

      <!-- 通知状态 -->
      <div class="bg-white p-4 rounded shadow">
        <h2 class="text-lg font-semibold mb-3">通知状态</h2>
        <div class="space-y-2 text-sm">
          <p>好友请求: {{ notificationsStore.friendRequests.length }}</p>
          <p>未读通知: {{ notificationsStore.unreadCount }}</p>
          <p>总未读: {{ notificationsStore.totalUnreadCount }}</p>
        </div>
        
        <div class="mt-4">
          <button 
            @click="simulateFriendRequest"
            class="w-full px-3 py-2 bg-blue-500 text-white rounded text-sm"
          >
            模拟好友请求事件
          </button>
        </div>
      </div>

      <!-- 事件日志 -->
      <div class="bg-white p-4 rounded shadow">
        <h2 class="text-lg font-semibold mb-3">事件日志</h2>
        <div class="h-40 overflow-y-auto bg-gray-100 p-2 rounded text-xs">
          <div v-for="(log, index) in eventLogs" :key="index" class="mb-1">
            <span class="text-gray-500">{{ log.time }}</span>
            <span :class="log.type === 'error' ? 'text-red-600' : 'text-blue-600'">
              {{ log.message }}
            </span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="mt-4">
      <RouterLink to="/" class="text-blue-500 underline">返回首页</RouterLink>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useNotificationsStore } from '@/stores/notifications'
import { socketService } from '@/services/socket'

const authStore = useAuthStore()
const notificationsStore = useNotificationsStore()

const socketConnected = ref(false)
const socketId = ref('')
const eventLogs = ref<Array<{time: string, message: string, type: string}>>([])

const addLog = (message: string, type: string = 'info') => {
  const time = new Date().toLocaleTimeString()
  eventLogs.value.push({ time, message, type })
  console.log(`[${time}] ${message}`)
}

const updateSocketStatus = () => {
  socketConnected.value = socketService.isConnected()
  socketId.value = socketService.id || ''
}

const connectSocket = async () => {
  try {
    addLog('尝试连接Socket...')
    await socketService.connect()
    updateSocketStatus()
    addLog('Socket连接成功')
  } catch (error) {
    addLog(`Socket连接失败: ${error}`, 'error')
  }
}

const disconnectSocket = () => {
  addLog('断开Socket连接')
  socketService.disconnect()
  updateSocketStatus()
}

const setupTestListeners = () => {
  addLog('设置测试事件监听器')
  
  // 清理之前的监听器
  socketService.off('friend_request_received')
  socketService.off('connected')
  socketService.off('error')
  
  // 设置新的监听器
  socketService.on('friend_request_received', (data) => {
    addLog(`收到好友请求事件: ${JSON.stringify(data)}`)
  })
  
  socketService.on('connected', (data) => {
    addLog(`Socket连接事件: ${JSON.stringify(data)}`)
  })
  
  socketService.on('error', (data) => {
    addLog(`Socket错误事件: ${JSON.stringify(data)}`, 'error')
  })
  
  addLog('测试监听器设置完成')
}

const sendTestEvent = () => {
  if (!socketConnected.value) {
    addLog('Socket未连接，无法发送事件', 'error')
    return
  }
  
  addLog('发送测试事件')
  socketService.emit('test_event', { message: 'Hello from debug page' })
}

const simulateFriendRequest = () => {
  addLog('模拟好友请求事件')
  const testRequest = {
    id: Date.now(),
    sender_id: 999,
    sender_username: '调试用户',
    sender_display_name: '调试用户',
    receiver_id: 1,
    receiver_username: '当前用户',
    receiver_display_name: '当前用户',
    status: 'pending' as const,
    message: '这是一个调试好友请求',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  
  // 直接触发事件处理
  notificationsStore.addFriendRequest(testRequest)
  addLog('好友请求已添加到store')
}

const clearLogs = () => {
  eventLogs.value = []
}

onMounted(() => {
  updateSocketStatus()
  addLog('Socket调试页面已加载')
  
  // 监听Socket连接状态变化
  const checkInterval = setInterval(() => {
    const wasConnected = socketConnected.value
    updateSocketStatus()
    if (wasConnected !== socketConnected.value) {
      addLog(`Socket状态变化: ${socketConnected.value ? '已连接' : '已断开'}`)
    }
  }, 1000)
  
  onUnmounted(() => {
    clearInterval(checkInterval)
  })
})
</script>
