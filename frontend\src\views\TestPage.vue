<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">测试页面</h1>
    <p>这是一个简单的测试页面</p>
    
    <div class="mt-4">
      <button 
        @click="count++"
        class="px-4 py-2 bg-blue-500 text-white rounded"
      >
        点击次数: {{ count }}
      </button>
    </div>
    
    <div class="mt-4">
      <RouterLink to="/" class="text-blue-500 underline">返回首页</RouterLink>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const count = ref(0)
</script>
