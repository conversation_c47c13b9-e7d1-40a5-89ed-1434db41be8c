#!/usr/bin/env python3
"""
测试好友请求通知功能
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:5000"
API_URL = f"{BASE_URL}/api"

def test_friend_request():
    """测试发送好友请求"""
    
    # 1. 创建两个测试用户
    print("🔧 创建测试用户...")
    
    # 用户1 (发送者)
    user1_data = {
        "username": "test_sender",
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    # 用户2 (接收者)
    user2_data = {
        "username": "test_receiver", 
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    # 注册用户1
    try:
        response1 = requests.post(f"{API_URL}/auth/register", json=user1_data)
        print(f"用户1注册: {response1.status_code}")
        if response1.status_code == 201:
            user1_info = response1.json()
            print(f"用户1 ID: {user1_info.get('user', {}).get('id')}")
        else:
            print(f"用户1注册失败: {response1.text}")
    except Exception as e:
        print(f"用户1注册异常: {e}")
    
    # 注册用户2
    try:
        response2 = requests.post(f"{API_URL}/auth/register", json=user2_data)
        print(f"用户2注册: {response2.status_code}")
        if response2.status_code == 201:
            user2_info = response2.json()
            print(f"用户2 ID: {user2_info.get('user', {}).get('id')}")
        else:
            print(f"用户2注册失败: {response2.text}")
    except Exception as e:
        print(f"用户2注册异常: {e}")
    
    # 2. 用户1登录
    print("\n🔧 用户1登录...")
    try:
        login_response = requests.post(f"{API_URL}/auth/login", json={
            "username": "test_sender",
            "password": "password123"
        })
        print(f"登录状态: {login_response.status_code}")
        
        if login_response.status_code == 200:
            # 获取cookies
            cookies = login_response.cookies
            print(f"获得cookies: {dict(cookies)}")
            
            # 3. 搜索用户2
            print("\n🔧 搜索接收者...")
            search_response = requests.get(f"{API_URL}/friends/search", 
                                         params={"q": "test_receiver"}, 
                                         cookies=cookies)
            print(f"搜索状态: {search_response.status_code}")
            
            if search_response.status_code == 200:
                search_data = search_response.json()
                users = search_data.get('users', [])
                if users:
                    receiver_id = users[0]['id']
                    print(f"找到接收者 ID: {receiver_id}")
                    
                    # 4. 发送好友请求
                    print("\n🔔 发送好友请求...")
                    friend_request_data = {
                        "receiver_id": receiver_id,
                        "message": "测试好友请求通知功能"
                    }
                    
                    request_response = requests.post(f"{API_URL}/friends/requests", 
                                                   json=friend_request_data,
                                                   cookies=cookies)
                    print(f"好友请求状态: {request_response.status_code}")
                    print(f"响应内容: {request_response.text}")
                    
                    if request_response.status_code == 201:
                        print("✅ 好友请求发送成功！")
                        print("📱 请检查前端是否收到通知")
                    else:
                        print(f"❌ 好友请求发送失败: {request_response.text}")
                else:
                    print("❌ 未找到接收者")
            else:
                print(f"❌ 搜索失败: {search_response.text}")
        else:
            print(f"❌ 登录失败: {login_response.text}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_friend_request()
